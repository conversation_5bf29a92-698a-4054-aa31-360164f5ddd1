'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { ShimmerText, PulseDot, BreathingCard } from '@/components/ui/LiveIndicator';
import { useStaggeredEntrance } from '@/hooks/useAnimations';
import { DEGEN_EMOJIS } from '@/lib/degen-terminology';
import { TELEGRAM_CONFIG } from '@/lib/constants';
import Icon from '@/components/ui/Icon';
import { GetStartedGuide } from '@/components/support/GetStartedGuide';
import { ContactForm } from '@/components/support/ContactForm';
import { TicketDashboard } from '@/components/support/TicketDashboard';

export default function SupportPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'guide' | 'tickets'>('overview');
  const [activeCategory, setActiveCategory] = useState('general');
  const [searchQuery, setSearchQuery] = useState('');
  const [isContactFormOpen, setIsContactFormOpen] = useState(false);
  const { containerRef } = useStaggeredEntrance(6, 100);

  const tabs = [
    { id: 'overview', name: 'Support Center', icon: 'target', color: 'text-degen-blue' },
    { id: 'guide', name: 'Get Started', icon: 'rocket', color: 'text-degen-purple' },
    { id: 'tickets', name: 'My Tickets', icon: 'chartUp', color: 'text-degen-green' },
  ];

  const supportCategories = [
    { id: 'general', name: 'General Help', icon: 'target', color: 'text-degen-blue' },
    { id: 'trading', name: 'Trading Issues', icon: 'trading', color: 'text-degen-green' },
    { id: 'wallet', name: 'Wallet Connection', icon: 'wallet', color: 'text-degen-purple' },
    { id: 'signals', name: 'Signal Problems', icon: 'signals', color: 'text-degen-gold' },
    { id: 'technical', name: 'Technical Support', icon: 'settings', color: 'text-degen-red' },
  ];

  const faqItems = [
    {
      category: 'general',
      question: 'How do I get started with Signal V1?',
      answer: 'Connect your Solana wallet, configure your trading settings, and start monitoring alpha signals from Telegram channels.',
    },
    {
      category: 'trading',
      question: 'Why did my trade fail?',
      answer: 'Common reasons include insufficient SOL balance, high slippage, or network congestion. Check your settings and try again.',
    },
    {
      category: 'wallet',
      question: 'My wallet won\'t connect. What should I do?',
      answer: 'Ensure you have a Solana wallet extension installed (Phantom, Solflare, etc.) and refresh the page.',
    },
    {
      category: 'signals',
      question: 'How are signals validated?',
      answer: 'Signals are validated by checking contract addresses, market cap data, and filtering out suspicious or invalid tokens.',
    },
    {
      category: 'technical',
      question: 'The app is running slowly. How can I fix this?',
      answer: 'Clear your browser cache, disable unnecessary browser extensions, and ensure you have a stable internet connection.',
    },
  ];

  const filteredFAQ = faqItems.filter(item => 
    item.category === activeCategory && 
    (searchQuery === '' || 
     item.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
     item.answer.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  return (
    <MainLayout>
      <div className="space-y-6" ref={containerRef as React.RefObject<HTMLDivElement>}>
        {/* Header */}
        <div className="text-center slide-up-enter">
          <ShimmerText speed="slow">
            <h1 className="text-4xl font-bold text-primary">
              Support Center
            </h1>
          </ShimmerText>
          <p className="mt-3 text-lg text-degen-purple font-medium fade-in-enter">
            Get help with Signal V1 - We're here to help you moon safely {DEGEN_EMOJIS.rocket}
          </p>
          <div className="flex items-center justify-center gap-2 mt-2">
            <PulseDot color="blue" size="sm" />
            <PulseDot color="purple" size="sm" />
            <PulseDot color="green" size="sm" />
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex justify-center fade-in-enter">
          <div className="flex gap-2 p-1 bg-gradient-sophisticated rounded-lg border border-degen-purple/30">
            {tabs.map((tab) => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? "ape" : "ghost"}
                size="sm"
                onClick={() => setActiveTab(tab.id as any)}
                className={`font-medium ${activeTab === tab.id ? 'glow-purple' : ''}`}
              >
                <Icon
                  name={tab.icon}
                  size="sm"
                  color={activeTab === tab.id ? "white" : "gray"}
                  className="mr-2"
                />
                {tab.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Tab Content */}
        <div className="fade-in-enter">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Quick Actions */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <BreathingCard intensity="subtle">
                  <Card className="bg-gradient-sophisticated border-gradient-blue text-center">
                    <CardContent className="p-6">
                      <div className="text-4xl mb-3">{DEGEN_EMOJIS.lightning}</div>
                      <h3 className="text-lg font-bold text-degen-blue mb-2">Quick Start Guide</h3>
                      <p className="text-degen-gray text-sm mb-4">
                        New to Signal V1? Get up and running in minutes
                      </p>
                      <Button
                        variant="diamond"
                        size="sm"
                        className="w-full"
                        onClick={() => setActiveTab('guide')}
                      >
                        <Icon name="rocket" size="sm" color="white" className="mr-1" />
                        Get Started
                      </Button>
                    </CardContent>
                  </Card>
                </BreathingCard>

                <BreathingCard intensity="subtle">
                  <Card className="bg-gradient-sophisticated border-gradient-purple text-center">
                    <CardContent className="p-6">
                      <div className="text-4xl mb-3">{DEGEN_EMOJIS.target}</div>
                      <h3 className="text-lg font-bold text-degen-purple mb-2">Contact Support</h3>
                      <p className="text-degen-gray text-sm mb-4">
                        Need personal help? Reach out to our team
                      </p>
                      <Button
                        variant="moon"
                        size="sm"
                        className="w-full"
                        onClick={() => setIsContactFormOpen(true)}
                      >
                        <Icon name="target" size="sm" color="white" className="mr-1" />
                        Contact Us
                      </Button>
                    </CardContent>
                  </Card>
                </BreathingCard>

                <BreathingCard intensity="subtle">
                  <Card className="bg-gradient-sophisticated border-gradient-green text-center">
                    <CardContent className="p-6">
                      <div className="text-4xl mb-3">{DEGEN_EMOJIS.chart}</div>
                      <h3 className="text-lg font-bold text-degen-green mb-2">System Status</h3>
                      <p className="text-degen-gray text-sm mb-4">
                        Check if all systems are operational
                      </p>
                      <Badge variant="moon" className="w-full justify-center">
                        <PulseDot color="green" size="sm" className="mr-1" />
                        All Systems Operational
                      </Badge>
                    </CardContent>
                  </Card>
                </BreathingCard>
              </div>

              {/* Search */}
              <Card className="bg-gradient-sophisticated border-gradient-blue">
                <CardContent className="p-6">
                  <Input
                    placeholder="Search for help topics..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="bg-gradient-neutral-subtle border-gradient-blue text-degen-blue placeholder:text-degen-gray"
                    leftIcon={
                      <svg className="w-4 h-4 text-degen-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    }
                  />
                </CardContent>
              </Card>

              {/* Categories */}
              <div className="flex flex-wrap gap-3 justify-center">
                {supportCategories.map((category) => (
                  <Button
                    key={category.id}
                    variant={activeCategory === category.id ? "ape" : "outline"}
                    size="sm"
                    onClick={() => setActiveCategory(category.id)}
                    className={`font-medium ${activeCategory === category.id ? 'glow-purple' : ''}`}
                  >
                    <Icon name={category.icon} size="sm" color={activeCategory === category.id ? "white" : "gray"} className="mr-1" />
                    {category.name}
                  </Button>
                ))}
              </div>

              {/* FAQ Section */}
              <div className="space-y-4">
                <h2 className="text-2xl font-bold text-primary text-center">
                  Frequently Asked Questions
                </h2>
          
          {filteredFAQ.length === 0 ? (
            <Card className="bg-gradient-degen border-degen-blue/30">
              <CardContent className="p-8 text-center">
                <div className="text-6xl mb-4">{DEGEN_EMOJIS.eyes}</div>
                <h3 className="text-xl font-bold text-degen-blue mb-2">No Results Found</h3>
                <p className="text-degen-gray">
                  Try adjusting your search or selecting a different category
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-4">
              {filteredFAQ.map((item, index) => (
                <BreathingCard key={index} intensity="subtle" className="scale-in-enter">
                  <Card className="bg-gradient-degen border-degen-purple/30 hover:glow-purple transition-all duration-300">
                    <CardHeader>
                      <CardTitle className="text-lg text-degen-gold flex items-center gap-2">
                        <PulseDot color="gold" size="sm" />
                        {item.question}
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-degen-blue leading-relaxed">
                        {item.answer}
                      </p>
                    </CardContent>
                  </Card>
                </BreathingCard>
              ))}
            </div>
          )}
        </div>

              {/* Contact Section */}
              <Card className="bg-gradient-sophisticated border-gradient-purple">
                <CardHeader>
                  <CardTitle className="text-center text-degen-purple">
                    Still Need Help?
                  </CardTitle>
                </CardHeader>
                <CardContent className="text-center space-y-4">
                  <p className="text-degen-gray">
                    Can't find what you're looking for? Our support team is ready to help you succeed.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      variant="moon"
                      className="font-bold"
                      onClick={() => setIsContactFormOpen(true)}
                    >
                      <Icon name="target" size="sm" color="white" className="mr-1" />
                      Open Support Ticket
                    </Button>
                    <Button
                      variant="diamond"
                      className="font-bold"
                      onClick={() => window.open(TELEGRAM_CONFIG.communityLinks.mainGroup, '_blank')}
                    >
                      <Icon name="signals" size="sm" color="white" className="mr-1" />
                      Join Telegram Community
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {activeTab === 'guide' && (
            <GetStartedGuide />
          )}

          {activeTab === 'tickets' && (
            <TicketDashboard />
          )}
        </div>

        {/* Contact Form Modal */}
        <ContactForm
          isOpen={isContactFormOpen}
          onClose={() => setIsContactFormOpen(false)}
        />
      </div>
    </MainLayout>
  );
}
