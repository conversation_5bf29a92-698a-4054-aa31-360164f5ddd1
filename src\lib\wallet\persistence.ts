// Wallet State Persistence Utility
// Handles wallet connection state persistence across page navigations and browser sessions

import { PublicKey } from '@solana/web3.js';

interface WalletState {
  isConnected: boolean;
  publicKey: string | null;
  walletName: string | null;
  lastConnected: number;
  autoReconnect: boolean;
}

interface WalletSession {
  sessionId: string;
  startTime: number;
  lastActivity: number;
  walletName: string;
  publicKey: string;
}

const STORAGE_KEYS = {
  WALLET_STATE: 'signal-v1-wallet-state',
  WALLET_SESSION: 'signal-v1-wallet-session',
  WALLET_PREFERENCES: 'signal-v1-wallet-preferences',
} as const;

const SESSION_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours
const ACTIVITY_TIMEOUT = 30 * 60 * 1000; // 30 minutes

export class WalletPersistence {
  private static instance: WalletPersistence;
  private sessionId: string;
  private activityTimer: NodeJS.Timeout | null = null;

  private constructor() {
    this.sessionId = this.generateSessionId();
    this.startActivityTracking();
  }

  static getInstance(): WalletPersistence {
    if (!WalletPersistence.instance) {
      WalletPersistence.instance = new WalletPersistence();
    }
    return WalletPersistence.instance;
  }

  /**
   * Save wallet connection state
   */
  saveWalletState(
    isConnected: boolean,
    publicKey: PublicKey | null,
    walletName: string | null,
    autoReconnect: boolean = true
  ): void {
    try {
      const state: WalletState = {
        isConnected,
        publicKey: publicKey?.toString() || null,
        walletName,
        lastConnected: Date.now(),
        autoReconnect,
      };

      localStorage.setItem(STORAGE_KEYS.WALLET_STATE, JSON.stringify(state));

      // Save session info if connected
      if (isConnected && publicKey && walletName) {
        this.saveWalletSession(walletName, publicKey.toString());
      } else {
        this.clearWalletSession();
      }

      console.log('💾 Wallet state saved:', { isConnected, walletName, publicKey: publicKey?.toString() });
    } catch (error) {
      console.error('Failed to save wallet state:', error);
    }
  }

  /**
   * Load wallet connection state
   */
  loadWalletState(): WalletState | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.WALLET_STATE);
      if (!stored) return null;

      const state: WalletState = JSON.parse(stored);

      // Check if state is still valid (not too old)
      const now = Date.now();
      if (now - state.lastConnected > SESSION_TIMEOUT) {
        console.log('🕐 Wallet state expired, clearing...');
        this.clearWalletState();
        return null;
      }

      console.log('📖 Wallet state loaded:', state);
      return state;
    } catch (error) {
      console.error('Failed to load wallet state:', error);
      return null;
    }
  }

  /**
   * Clear wallet connection state
   */
  clearWalletState(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.WALLET_STATE);
      this.clearWalletSession();
      console.log('🧹 Wallet state cleared');
    } catch (error) {
      console.error('Failed to clear wallet state:', error);
    }
  }

  /**
   * Save wallet session info
   */
  private saveWalletSession(walletName: string, publicKey: string): void {
    try {
      const session: WalletSession = {
        sessionId: this.sessionId,
        startTime: Date.now(),
        lastActivity: Date.now(),
        walletName,
        publicKey,
      };

      localStorage.setItem(STORAGE_KEYS.WALLET_SESSION, JSON.stringify(session));
    } catch (error) {
      console.error('Failed to save wallet session:', error);
    }
  }

  /**
   * Load wallet session info
   */
  loadWalletSession(): WalletSession | null {
    try {
      const stored = localStorage.getItem(STORAGE_KEYS.WALLET_SESSION);
      if (!stored) return null;

      const session: WalletSession = JSON.parse(stored);

      // Check if session is still valid
      const now = Date.now();
      if (now - session.lastActivity > ACTIVITY_TIMEOUT) {
        console.log('🕐 Wallet session expired');
        this.clearWalletSession();
        return null;
      }

      return session;
    } catch (error) {
      console.error('Failed to load wallet session:', error);
      return null;
    }
  }

  /**
   * Update session activity
   */
  updateActivity(): void {
    try {
      const session = this.loadWalletSession();
      if (session) {
        session.lastActivity = Date.now();
        localStorage.setItem(STORAGE_KEYS.WALLET_SESSION, JSON.stringify(session));
      }
    } catch (error) {
      console.error('Failed to update activity:', error);
    }
  }

  /**
   * Clear wallet session
   */
  private clearWalletSession(): void {
    try {
      localStorage.removeItem(STORAGE_KEYS.WALLET_SESSION);
    } catch (error) {
      console.error('Failed to clear wallet session:', error);
    }
  }

  /**
   * Check if wallet should auto-reconnect
   */
  shouldAutoReconnect(): boolean {
    const state = this.loadWalletState();
    const session = this.loadWalletSession();
    
    return !!(state?.autoReconnect && state?.isConnected && session);
  }

  /**
   * Get stored wallet info for reconnection
   */
  getStoredWalletInfo(): { walletName: string; publicKey: string } | null {
    const state = this.loadWalletState();
    if (state?.walletName && state?.publicKey) {
      return {
        walletName: state.walletName,
        publicKey: state.publicKey,
      };
    }
    return null;
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Start activity tracking
   */
  private startActivityTracking(): void {
    // Update activity on user interactions
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
    
    const updateActivity = () => {
      this.updateActivity();
    };

    events.forEach(event => {
      document.addEventListener(event, updateActivity, { passive: true });
    });

    // Periodic activity check
    this.activityTimer = setInterval(() => {
      const session = this.loadWalletSession();
      if (!session) {
        console.log('🔌 No active wallet session detected');
      }
    }, 60000); // Check every minute
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    if (this.activityTimer) {
      clearInterval(this.activityTimer);
      this.activityTimer = null;
    }
  }
}

// Export singleton instance
export const walletPersistence = WalletPersistence.getInstance();
