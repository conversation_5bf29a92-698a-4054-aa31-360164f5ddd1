// Telegram Signal Parser - Extracts trading signals from Telegram messages

import { TELEGRAM_CONFIG, VALIDATION_RULES } from '../constants';
import type { ParsedSignal } from '@/types';

export class SignalParser {
  /**
   * Parse a Telegram message for trading signals
   */
  static parseMessage(
    content: string,
    channelId: string,
    messageId: number,
    timestamp: Date,
    images?: string[]
  ): ParsedSignal | null {
    try {
      // Check if message contains any trigger phrases
      const triggerPhrase = this.findTriggerPhrase(content);
      if (!triggerPhrase) {
        return null;
      }

      // Extract token contract address
      const tokenAddress = this.extractTokenAddress(content);
      if (!tokenAddress) {
        return null;
      }

      // Extract additional information
      const tokenSymbol = this.extractTokenSymbol(content);
      const marketCap = this.extractMarketCap(content);

      // Calculate confidence score
      const confidence = this.calculateConfidence(content, triggerPhrase, tokenAddress, tokenSymbol);

      return {
        tokenAddress,
        tokenSymbol,
        marketCap,
        triggerPhrase,
        confidence,
        metadata: {
          channelId,
          messageId,
          timestamp,
          images,
        },
      };
    } catch (error) {
      console.error('Error parsing signal:', error);
      return null;
    }
  }

  /**
   * Find trigger phrase in message content
   */
  private static findTriggerPhrase(content: string): string | null {
    const normalizedContent = content.toLowerCase();
    
    for (const trigger of TELEGRAM_CONFIG.signalTriggers) {
      if (normalizedContent.includes(trigger.toLowerCase())) {
        return trigger;
      }
    }
    
    return null;
  }

  /**
   * Extract Solana token contract address from message
   */
  private static extractTokenAddress(content: string): string | null {
    // Common patterns for Solana addresses in messages
    const patterns = [
      // Direct address mention
      /\b([1-9A-HJ-NP-Za-km-z]{44})\b/g,
      // CA: prefix
      /CA:\s*([1-9A-HJ-NP-Za-km-z]{44})/gi,
      // Contract: prefix
      /Contract:\s*([1-9A-HJ-NP-Za-km-z]{44})/gi,
      // Address: prefix
      /Address:\s*([1-9A-HJ-NP-Za-km-z]{44})/gi,
      // Token: prefix
      /Token:\s*([1-9A-HJ-NP-Za-km-z]{44})/gi,
    ];

    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches) {
        for (const match of matches) {
          // Extract just the address part
          const address = match.replace(/^(CA:|Contract:|Address:|Token:)\s*/i, '').trim();
          
          // Validate the address
          if (this.isValidSolanaAddress(address)) {
            return address;
          }
        }
      }
    }

    return null;
  }

  /**
   * Extract token symbol from message
   */
  private static extractTokenSymbol(content: string): string | null {
    // Common patterns for token symbols
    const patterns = [
      // $SYMBOL format
      /\$([A-Z]{2,10})\b/g,
      // Symbol: prefix
      /Symbol:\s*([A-Z]{2,10})\b/gi,
      // Token symbol in parentheses
      /\(([A-Z]{2,10})\)/g,
      // Ticker: prefix
      /Ticker:\s*([A-Z]{2,10})\b/gi,
    ];

    for (const pattern of patterns) {
      const matches = content.match(pattern);
      if (matches && matches.length > 0) {
        // Return the first valid symbol found
        const symbol = matches[0].replace(/[\$\(\)]/g, '').replace(/^(Symbol:|Ticker:)\s*/i, '').trim();
        if (symbol.length >= 2 && symbol.length <= 10) {
          return symbol.toUpperCase();
        }
      }
    }

    return null;
  }

  /**
   * Extract market cap from message
   */
  private static extractMarketCap(content: string): number | null {
    // Common patterns for market cap
    const patterns = [
      // Market Cap: $1.5M
      /Market\s*Cap:\s*\$?([\d,]*\.?\d+)\s*([KMB])?/gi,
      // MC: $1.5M
      /MC:\s*\$?([\d,]*\.?\d+)\s*([KMB])?/gi,
      // $1.5M MC
      /\$?([\d,]*\.?\d+)\s*([KMB])?\s*MC/gi,
      // Cap: $1.5M
      /Cap:\s*\$?([\d,]*\.?\d+)\s*([KMB])?/gi,
    ];

    for (const pattern of patterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        const value = parseFloat(match[1].replace(/,/g, ''));
        const unit = match[2]?.toUpperCase();
        
        if (!isNaN(value)) {
          let multiplier = 1;
          switch (unit) {
            case 'K':
              multiplier = 1000;
              break;
            case 'M':
              multiplier = 1000000;
              break;
            case 'B':
              multiplier = 1000000000;
              break;
          }
          
          return value * multiplier;
        }
      }
    }

    return null;
  }

  /**
   * Calculate confidence score for the parsed signal
   */
  private static calculateConfidence(
    content: string,
    triggerPhrase: string,
    tokenAddress: string,
    tokenSymbol?: string
  ): number {
    let confidence = 0.5; // Base confidence

    // Strong trigger phrases get higher confidence
    const strongTriggers = ['🚨Alerted at', '🛎️First Hit'];
    if (strongTriggers.includes(triggerPhrase)) {
      confidence += 0.3;
    } else {
      confidence += 0.1;
    }

    // Valid token address increases confidence
    if (this.isValidSolanaAddress(tokenAddress)) {
      confidence += 0.2;
    }

    // Token symbol presence increases confidence
    if (tokenSymbol) {
      confidence += 0.1;
    }

    // Additional indicators
    const indicators = [
      /buy\s*now/gi,
      /entry/gi,
      /target/gi,
      /stop\s*loss/gi,
      /take\s*profit/gi,
      /moon/gi,
      /gem/gi,
      /x\d+/gi, // multiplier like x10, x100
    ];

    for (const indicator of indicators) {
      if (content.match(indicator)) {
        confidence += 0.05;
      }
    }

    // Cap confidence at 1.0
    return Math.min(confidence, 1.0);
  }

  /**
   * Validate Solana address format
   */
  private static isValidSolanaAddress(address: string): boolean {
    if (!address || address.length !== VALIDATION_RULES.solanaAddress.length) {
      return false;
    }
    return VALIDATION_RULES.solanaAddress.pattern.test(address);
  }

  /**
   * Extract price information from message
   */
  static extractPriceInfo(content: string): { price?: number; priceChange?: number } {
    const result: { price?: number; priceChange?: number } = {};

    // Price patterns
    const pricePatterns = [
      /Price:\s*\$?([\d,]+\.?\d*)/gi,
      /\$?([\d,]+\.?\d*)\s*USD/gi,
      /Current:\s*\$?([\d,]+\.?\d*)/gi,
    ];

    for (const pattern of pricePatterns) {
      const match = content.match(pattern);
      if (match && match[1]) {
        const price = parseFloat(match[1].replace(/,/g, ''));
        if (!isNaN(price)) {
          result.price = price;
          break;
        }
      }
    }

    // Price change patterns
    const changePatterns = [
      /([+-]?\d+\.?\d*)%/g,
      /Change:\s*([+-]?\d+\.?\d*)%/gi,
      /24h:\s*([+-]?\d+\.?\d*)%/gi,
    ];

    for (const pattern of changePatterns) {
      const match = content.match(pattern);
      if (match) {
        const change = parseFloat(match[1]);
        if (!isNaN(change)) {
          result.priceChange = change;
          break;
        }
      }
    }

    return result;
  }

  /**
   * Check if message is likely a scam or spam
   */
  static isLikelyScam(content: string): boolean {
    const scamIndicators = [
      /guaranteed/gi,
      /100%\s*profit/gi,
      /risk\s*free/gi,
      /send\s*me\s*money/gi,
      /private\s*message\s*me/gi,
      /telegram\.me/gi,
      /t\.me/gi,
      /click\s*here/gi,
      /limited\s*time/gi,
      /act\s*fast/gi,
      /don't\s*miss/gi,
      /last\s*chance/gi,
    ];

    for (const indicator of scamIndicators) {
      if (content.match(indicator)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Validate signal quality
   */
  static validateSignal(signal: ParsedSignal): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check token address
    if (!this.isValidSolanaAddress(signal.tokenAddress)) {
      errors.push('Invalid Solana token address');
    }

    // Check confidence threshold
    if (signal.confidence < 0.6) {
      errors.push('Low confidence signal');
    }

    // Check for scam indicators
    const messageContent = `${signal.triggerPhrase} ${signal.tokenSymbol || ''}`;
    if (this.isLikelyScam(messageContent)) {
      errors.push('Potential scam or spam signal');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}
