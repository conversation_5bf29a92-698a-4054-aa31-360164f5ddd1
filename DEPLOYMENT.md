# Deployment Guide - Signal V1

This guide covers deploying Signal V1 to various platforms and environments.

## 🚀 Quick Deploy Options

### Vercel (Recommended)
Vercel provides the easiest deployment experience for Next.js applications.

#### One-Click Deploy
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/signal-v1)

#### Manual Deployment
1. **Install Vercel CLI**
   ```bash
   npm i -g vercel
   ```

2. **Deploy to Vercel**
   ```bash
   vercel --prod
   ```

3. **Configure Environment Variables**
   Add the following in your Vercel dashboard:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
   NEXT_PUBLIC_SOLANA_NETWORK=mainnet-beta
   TELEGRAM_API_ID=your_api_id
   TELEGRAM_API_HASH=your_api_hash
   TELEGRAM_PHONE_NUMBER=+**********
   NEXT_PUBLIC_APP_URL=https://your-domain.vercel.app
   ```

### Netlify
1. **Connect Repository**
   - Go to [Netlify](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository

2. **Build Settings**
   ```
   Build command: npm run build
   Publish directory: .next
   ```

3. **Environment Variables**
   Add the same environment variables as listed above in Netlify's dashboard.

## 🐳 Docker Deployment

### Build Docker Image
```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Build and Run
```bash
# Build the image
docker build -t signal-v1 .

# Run the container
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_SUPABASE_URL=your_supabase_url \
  -e NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key \
  -e NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com \
  signal-v1
```

### Docker Compose
```yaml
# docker-compose.yml
version: '3.8'

services:
  signal-v1:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_SUPABASE_URL=${NEXT_PUBLIC_SUPABASE_URL}
      - NEXT_PUBLIC_SUPABASE_ANON_KEY=${NEXT_PUBLIC_SUPABASE_ANON_KEY}
      - SUPABASE_SERVICE_ROLE_KEY=${SUPABASE_SERVICE_ROLE_KEY}
      - NEXT_PUBLIC_SOLANA_RPC_URL=${NEXT_PUBLIC_SOLANA_RPC_URL}
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
    restart: unless-stopped
```

Run with:
```bash
docker-compose up -d
```

## ☁️ Cloud Platform Deployments

### AWS (Elastic Beanstalk)
1. **Install EB CLI**
   ```bash
   pip install awsebcli
   ```

2. **Initialize and Deploy**
   ```bash
   eb init
   eb create production
   eb deploy
   ```

3. **Configure Environment Variables**
   ```bash
   eb setenv NEXT_PUBLIC_SUPABASE_URL=your_url \
            NEXT_PUBLIC_SUPABASE_ANON_KEY=your_key \
            NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
   ```

### Google Cloud Platform (Cloud Run)
1. **Build and Push to Container Registry**
   ```bash
   gcloud builds submit --tag gcr.io/PROJECT_ID/signal-v1
   ```

2. **Deploy to Cloud Run**
   ```bash
   gcloud run deploy signal-v1 \
     --image gcr.io/PROJECT_ID/signal-v1 \
     --platform managed \
     --region us-central1 \
     --allow-unauthenticated
   ```

### Azure (Container Instances)
1. **Build and Push to Azure Container Registry**
   ```bash
   az acr build --registry myregistry --image signal-v1 .
   ```

2. **Deploy to Container Instances**
   ```bash
   az container create \
     --resource-group myResourceGroup \
     --name signal-v1 \
     --image myregistry.azurecr.io/signal-v1:latest \
     --ports 3000
   ```

## 🗄️ Database Setup

### Supabase Setup
1. **Create Supabase Project**
   - Go to [Supabase](https://supabase.com)
   - Create a new project
   - Note your project URL and anon key

2. **Run Database Schema**
   ```sql
   -- Run this in Supabase SQL editor
   -- User profiles table
   CREATE TABLE user_profiles (
     id UUID REFERENCES auth.users(id) PRIMARY KEY,
     email TEXT,
     wallet_address TEXT,
     preferences JSONB DEFAULT '{}',
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Portfolios table
   CREATE TABLE portfolios (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     user_id UUID REFERENCES user_profiles(id),
     name TEXT DEFAULT 'Default Portfolio',
     total_value DECIMAL DEFAULT 0,
     total_pnl DECIMAL DEFAULT 0,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Positions table
   CREATE TABLE positions (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     portfolio_id UUID REFERENCES portfolios(id),
     token_address TEXT NOT NULL,
     token_symbol TEXT,
     token_name TEXT,
     amount DECIMAL NOT NULL,
     entry_price DECIMAL NOT NULL,
     current_price DECIMAL NOT NULL,
     entry_value DECIMAL NOT NULL,
     current_value DECIMAL NOT NULL,
     pnl DECIMAL DEFAULT 0,
     pnl_percentage DECIMAL DEFAULT 0,
     multiplier DECIMAL DEFAULT 1,
     status TEXT DEFAULT 'active',
     entry_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     exit_date TIMESTAMP WITH TIME ZONE,
     signal_id TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Trades table
   CREATE TABLE trades (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     portfolio_id UUID REFERENCES portfolios(id),
     position_id UUID REFERENCES positions(id),
     type TEXT NOT NULL, -- 'buy' or 'sell'
     token_address TEXT NOT NULL,
     token_symbol TEXT,
     amount DECIMAL NOT NULL,
     price DECIMAL NOT NULL,
     value DECIMAL NOT NULL,
     slippage INTEGER,
     fees DECIMAL DEFAULT 0,
     signature TEXT,
     status TEXT DEFAULT 'pending',
     timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     signal_id TEXT,
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
   );

   -- Telegram signals table
   CREATE TABLE telegram_signals (
     id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
     channel_id TEXT NOT NULL,
     message_id BIGINT NOT NULL,
     content TEXT NOT NULL,
     token_address TEXT,
     token_symbol TEXT,
     token_name TEXT,
     market_cap BIGINT,
     price DECIMAL,
     price_change DECIMAL,
     trigger_phrase TEXT,
     confidence DECIMAL,
     valid BOOLEAN DEFAULT false,
     processed BOOLEAN DEFAULT false,
     metadata JSONB DEFAULT '{}',
     timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
     UNIQUE(channel_id, message_id)
   );

   -- Enable Row Level Security
   ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
   ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
   ALTER TABLE positions ENABLE ROW LEVEL SECURITY;
   ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
   ALTER TABLE telegram_signals ENABLE ROW LEVEL SECURITY;

   -- Create policies
   CREATE POLICY "Users can view own profile" ON user_profiles
     FOR SELECT USING (auth.uid() = id);

   CREATE POLICY "Users can update own profile" ON user_profiles
     FOR UPDATE USING (auth.uid() = id);

   CREATE POLICY "Users can view own portfolios" ON portfolios
     FOR SELECT USING (auth.uid() = user_id);

   CREATE POLICY "Users can view own positions" ON positions
     FOR SELECT USING (auth.uid() = (SELECT user_id FROM portfolios WHERE id = portfolio_id));

   CREATE POLICY "Users can view own trades" ON trades
     FOR SELECT USING (auth.uid() = (SELECT user_id FROM portfolios WHERE id = portfolio_id));

   CREATE POLICY "Anyone can view signals" ON telegram_signals
     FOR SELECT USING (true);
   ```

3. **Configure Authentication**
   - Enable email authentication in Supabase dashboard
   - Configure OAuth providers if needed
   - Set up custom SMTP for emails (optional)

## 🔧 Environment Configuration

### Production Environment Variables
```env
# Application
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Solana
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
NEXT_PUBLIC_SOLANA_NETWORK=mainnet-beta

# Telegram User Account Authentication (Recommended)
TELEGRAM_API_ID=your_api_id
TELEGRAM_API_HASH=your_api_hash
TELEGRAM_PHONE_NUMBER=+**********
TELEGRAM_SESSION_STRING=auto_generated_after_auth

# Legacy Telegram Bot API (Deprecated)
# TELEGRAM_BOT_TOKEN=your_bot_token
# TELEGRAM_WEBHOOK_URL=https://your-domain.com/api/telegram/webhook

# Security
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=https://your-domain.com

# Analytics (Optional)
NEXT_PUBLIC_GA_ID=your_google_analytics_id
```

### Development vs Production
- **Development**: Use devnet for Solana transactions
- **Production**: Use mainnet-beta for real trading
- **Testing**: Use testnet for integration tests

## 🔒 Security Considerations

### SSL/TLS
- Always use HTTPS in production
- Configure SSL certificates
- Enable HSTS headers

### Environment Security
- Never commit `.env` files
- Use secure secret management
- Rotate API keys regularly
- Enable 2FA on all accounts

### Rate Limiting
- Configure rate limiting for API endpoints
- Monitor for unusual activity
- Set up alerts for high error rates

## 📊 Monitoring & Analytics

### Application Monitoring
- Set up error tracking (Sentry)
- Monitor performance metrics
- Track user analytics
- Set up uptime monitoring

### Database Monitoring
- Monitor Supabase usage
- Set up backup schedules
- Track query performance
- Monitor connection limits

## 🚀 CI/CD Pipeline

### GitHub Actions
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run test:ci
      - run: npm run type-check

  deploy:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      - run: npm ci
      - run: npm run build
      - uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 🆘 Troubleshooting

### Common Issues
1. **Build Failures**: Check environment variables and dependencies
2. **Database Connection**: Verify Supabase credentials and network access
3. **Wallet Connection**: Ensure proper wallet adapter configuration
4. **API Limits**: Monitor rate limits and usage quotas

### Debug Commands
```bash
# Check build output
npm run build

# Verify environment variables
npm run env-check

# Test database connection
npm run db-test

# Run health checks
npm run health-check
```

---

For additional support, please refer to the main [README.md](README.md) or open an issue on GitHub.
