'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Badge } from '@/components/ui/Badge';
import { ShimmerText, PulseDot, BreathingCard } from '@/components/ui/LiveIndicator';
import { TicketCard } from './TicketCard';
import { DEGEN_EMOJIS } from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import { SupportService } from '@/lib/supabase/services/supportService';
import { useSupabaseAuth } from '@/hooks/useSupabase';
import { cn } from '@/lib/utils';

interface TicketStats {
  total: number;
  open: number;
  in_progress: number;
  resolved: number;
  closed: number;
}

interface Ticket {
  id: string;
  ticket_number: string;
  title: string;
  description: string;
  category: 'technical' | 'trading' | 'account' | 'feature_request' | 'general';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  created_at: string;
  updated_at: string;
  resolved_at?: string | null;
}

export function TicketDashboard() {
  const { user } = useSupabaseAuth();
  const [tickets, setTickets] = useState<Ticket[]>([]);
  const [filteredTickets, setFilteredTickets] = useState<Ticket[]>([]);
  const [stats, setStats] = useState<TicketStats>({
    total: 0,
    open: 0,
    in_progress: 0,
    resolved: 0,
    closed: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const statusFilters = [
    { value: 'all', label: 'All Tickets', color: 'text-degen-blue' },
    { value: 'open', label: 'Open', color: 'text-degen-blue' },
    { value: 'in_progress', label: 'In Progress', color: 'text-degen-gold' },
    { value: 'resolved', label: 'Resolved', color: 'text-degen-green' },
    { value: 'closed', label: 'Closed', color: 'text-degen-gray' },
  ];

  useEffect(() => {
    if (user?.id) {
      loadTickets();
      loadStats();
    }
  }, [user?.id]);

  useEffect(() => {
    filterTickets();
  }, [tickets, searchQuery, statusFilter]);

  const loadTickets = async () => {
    if (!user?.id) return;

    setLoading(true);
    setError(null);

    try {
      const { data, error } = await SupportService.getUserTickets(user.id);
      
      if (error) {
        setError(error);
      } else {
        setTickets(data || []);
      }
    } catch (err) {
      setError('Failed to load tickets');
    } finally {
      setLoading(false);
    }
  };

  const loadStats = async () => {
    if (!user?.id) return;

    try {
      const { data, error } = await SupportService.getTicketStats(user.id);
      
      if (error) {
        console.error('Error loading stats:', error);
      } else {
        setStats(data || {
          total: 0,
          open: 0,
          in_progress: 0,
          resolved: 0,
          closed: 0,
        });
      }
    } catch (err) {
      console.error('Error loading stats:', err);
    }
  };

  const filterTickets = () => {
    let filtered = tickets;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(ticket => ticket.status === statusFilter);
    }

    // Filter by search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(ticket =>
        ticket.title.toLowerCase().includes(query) ||
        ticket.description.toLowerCase().includes(query) ||
        ticket.ticket_number.toLowerCase().includes(query)
      );
    }

    setFilteredTickets(filtered);
  };

  const handleCloseTicket = async (ticketId: string) => {
    try {
      const { error } = await SupportService.closeTicket(ticketId);
      
      if (error) {
        console.error('Error closing ticket:', error);
      } else {
        // Refresh tickets and stats
        loadTickets();
        loadStats();
      }
    } catch (err) {
      console.error('Error closing ticket:', err);
    }
  };

  if (!user) {
    return (
      <Card className="bg-gradient-sophisticated border-gradient-blue text-center">
        <CardContent className="p-8">
          <div className="text-4xl mb-4">{DEGEN_EMOJIS.wallet}</div>
          <h3 className="text-lg font-bold text-degen-blue mb-2">
            Connect Your Wallet
          </h3>
          <p className="text-degen-gray">
            Please connect your wallet to view your support tickets
          </p>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="bg-gradient-sophisticated animate-pulse">
              <CardContent className="p-4">
                <div className="h-4 bg-degen-gray/30 rounded mb-2"></div>
                <div className="h-8 bg-degen-gray/30 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="space-y-4">
          {[...Array(3)].map((_, i) => (
            <Card key={i} className="bg-gradient-sophisticated animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-degen-gray/30 rounded mb-2"></div>
                <div className="h-4 bg-degen-gray/30 rounded mb-4 w-3/4"></div>
                <div className="h-16 bg-degen-gray/30 rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="bg-gradient-sophisticated border-gradient-red text-center">
        <CardContent className="p-8">
          <div className="text-4xl mb-4">{DEGEN_EMOJIS.rekt}</div>
          <h3 className="text-lg font-bold text-degen-red mb-2">
            Error Loading Tickets
          </h3>
          <p className="text-degen-gray mb-4">{error}</p>
          <Button variant="ape" onClick={loadTickets}>
            <Icon name="refresh" size="sm" color="white" className="mr-1" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <ShimmerText speed="slow">
          <h2 className="text-3xl font-bold text-primary">
            Support Tickets
          </h2>
        </ShimmerText>
        <p className="mt-2 text-lg text-degen-purple font-medium">
          Track your support requests and get help {DEGEN_EMOJIS.target}
        </p>
        <div className="flex items-center justify-center gap-2 mt-2">
          <PulseDot color="blue" size="sm" />
          <PulseDot color="purple" size="sm" />
          <PulseDot color="green" size="sm" />
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <BreathingCard intensity="subtle">
          <Card className="bg-gradient-sophisticated border-gradient-blue text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-degen-blue mb-1">{stats.total}</div>
              <div className="text-xs text-degen-gray">Total Tickets</div>
            </CardContent>
          </Card>
        </BreathingCard>
        
        <BreathingCard intensity="subtle">
          <Card className="bg-gradient-sophisticated border-gradient-blue text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-degen-blue mb-1">{stats.open}</div>
              <div className="text-xs text-degen-gray">Open</div>
            </CardContent>
          </Card>
        </BreathingCard>
        
        <BreathingCard intensity="subtle">
          <Card className="bg-gradient-sophisticated border-gradient-gold text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-degen-gold mb-1">{stats.in_progress}</div>
              <div className="text-xs text-degen-gray">In Progress</div>
            </CardContent>
          </Card>
        </BreathingCard>
        
        <BreathingCard intensity="subtle">
          <Card className="bg-gradient-sophisticated border-gradient-green text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-degen-green mb-1">{stats.resolved}</div>
              <div className="text-xs text-degen-gray">Resolved</div>
            </CardContent>
          </Card>
        </BreathingCard>
        
        <BreathingCard intensity="subtle">
          <Card className="bg-gradient-sophisticated border-gradient-gray text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-degen-gray mb-1">{stats.closed}</div>
              <div className="text-xs text-degen-gray">Closed</div>
            </CardContent>
          </Card>
        </BreathingCard>
      </div>

      {/* Filters */}
      <Card className="bg-gradient-sophisticated border-gradient-blue">
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <Input
                placeholder="Search tickets by title, description, or ticket number..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-gradient-neutral-subtle border-gradient-blue text-white placeholder:text-degen-gray"
                leftIcon={
                  <Icon name="search" size="sm" color="gray" />
                }
              />
            </div>
            
            {/* Status Filter */}
            <div className="flex gap-2 flex-wrap">
              {statusFilters.map((filter) => (
                <Button
                  key={filter.value}
                  variant={statusFilter === filter.value ? "ape" : "outline"}
                  size="sm"
                  onClick={() => setStatusFilter(filter.value)}
                  className={cn(
                    'text-xs',
                    statusFilter === filter.value && 'glow-purple'
                  )}
                >
                  {filter.label}
                  {filter.value !== 'all' && (
                    <Badge variant="outline" className="ml-1 text-xs">
                      {stats[filter.value as keyof TicketStats]}
                    </Badge>
                  )}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tickets List */}
      {filteredTickets.length === 0 ? (
        <Card className="bg-gradient-sophisticated border-gradient-purple text-center">
          <CardContent className="p-8">
            <div className="text-4xl mb-4">{DEGEN_EMOJIS.diamond}</div>
            <h3 className="text-lg font-bold text-degen-purple mb-2">
              {searchQuery || statusFilter !== 'all' ? 'No Matching Tickets' : 'No Support Tickets Yet'}
            </h3>
            <p className="text-degen-gray">
              {searchQuery || statusFilter !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'When you create support tickets, they\'ll appear here'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredTickets.map((ticket) => (
            <TicketCard
              key={ticket.id}
              ticket={ticket}
              onClose={handleCloseTicket}
            />
          ))}
        </div>
      )}
    </div>
  );
}
