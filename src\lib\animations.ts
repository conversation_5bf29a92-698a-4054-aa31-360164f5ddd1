/**
 * Animation utilities for the Signal V1 trading bot
 * Provides sophisticated visual effects and micro-interactions
 */

export const ANIMATION_DURATIONS = {
  fast: 150,
  normal: 300,
  slow: 500,
  breathe: 4000,
  shimmer: 2000,
  glow: 3000,
} as const;

export const ANIMATION_EASINGS = {
  smooth: 'cubic-bezier(0.4, 0, 0.2, 1)',
  bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
  elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
  sharp: 'cubic-bezier(0.4, 0, 1, 1)',
  standard: 'cubic-bezier(0.4, 0, 0.2, 1)',
} as const;

/**
 * Animation class generators for consistent styling
 */
export const createAnimationClasses = {
  /**
   * Creates a pulsing effect for live data indicators
   */
  pulse: (color: 'green' | 'red' | 'blue' | 'gold' | 'purple' = 'green') => ({
    green: 'animate-pulse text-degen-green glow-green',
    red: 'animate-pulse text-degen-red glow-red',
    blue: 'animate-pulse text-degen-blue glow-blue',
    gold: 'animate-pulse text-degen-gold glow-gold',
    purple: 'animate-pulse text-degen-purple glow-purple',
  }[color]),

  /**
   * Creates a breathing effect for cards and containers
   */
  breathe: (intensity: 'subtle' | 'normal' | 'strong' = 'normal') => ({
    subtle: 'breathe-effect opacity-90',
    normal: 'breathe-effect',
    strong: 'breathe-effect scale-105',
  }[intensity]),

  /**
   * Creates a shimmer effect for loading states and highlights
   */
  shimmer: (speed: 'slow' | 'normal' | 'fast' = 'normal') => ({
    slow: 'shimmer-effect [animation-duration:3s]',
    normal: 'shimmer-effect',
    fast: 'shimmer-effect [animation-duration:1s]',
  }[speed]),

  /**
   * Creates entrance animations
   */
  entrance: (type: 'fade' | 'slide' | 'scale' = 'fade') => ({
    fade: 'fade-in-enter',
    slide: 'slide-up-enter',
    scale: 'scale-in-enter',
  }[type]),

  /**
   * Creates hover effects for interactive elements
   */
  hover: (effect: 'lift' | 'glow' | 'scale' | 'all' = 'all') => {
    const effects = {
      lift: 'hover-lift',
      glow: 'hover:glow-blue',
      scale: 'hover:scale-105',
      all: 'hover-lift hover:glow-blue hover:scale-105',
    };
    return `transition-all duration-300 ${effects[effect]}`;
  },

  /**
   * Creates loading state animations
   */
  loading: (type: 'shimmer' | 'pulse' | 'spin' = 'shimmer') => ({
    shimmer: 'loading-shimmer',
    pulse: 'loading-pulse',
    spin: 'animate-spin',
  }[type]),
};

/**
 * Price change animation utilities
 */
export const priceChangeAnimation = {
  /**
   * Gets the appropriate animation class for price changes
   */
  getChangeClass: (currentValue: number, previousValue: number) => {
    if (currentValue > previousValue) {
      return 'price-up pulse-data';
    } else if (currentValue < previousValue) {
      return 'price-down pulse-data';
    }
    return 'price-neutral';
  },

  /**
   * Gets the appropriate color for price changes
   */
  getChangeColor: (change: number) => {
    if (change > 0) return 'degen-green';
    if (change < 0) return 'degen-red';
    return 'degen-blue';
  },

  /**
   * Creates a data update flash effect
   */
  flashUpdate: () => 'pulse-data',
};

/**
 * Interactive element animations
 */
export const interactiveAnimations = {
  /**
   * Button click ripple effect
   */
  buttonRipple: 'btn-enhanced',

  /**
   * Card hover and interaction effects
   */
  cardInteractive: 'interactive-card',

  /**
   * Live indicator effects
   */
  liveIndicator: (type: 'green' | 'red' | 'blue' = 'green') => `live-indicator ${type}`,

  /**
   * Glow pulse for important elements
   */
  glowPulse: 'glow-pulse-effect',
};

/**
 * Layout and container animations
 */
export const layoutAnimations = {
  /**
   * Staggered entrance for lists
   */
  staggeredEntrance: (delay: number) => ({
    animationDelay: `${delay * 100}ms`,
    className: 'slide-up-enter',
  }),

  /**
   * Grid item animations
   */
  gridItem: (index: number) => ({
    animationDelay: `${index * 50}ms`,
    className: 'scale-in-enter',
  }),

  /**
   * Page transition effects
   */
  pageTransition: 'fade-in-enter',
};

/**
 * Utility functions for dynamic animations
 */
export const animationUtils = {
  /**
   * Adds a temporary animation class and removes it after duration
   */
  addTemporaryClass: (element: HTMLElement, className: string, duration: number = 800) => {
    element.classList.add(className);
    setTimeout(() => {
      element.classList.remove(className);
    }, duration);
  },

  /**
   * Creates a staggered animation for multiple elements
   */
  staggerElements: (elements: NodeListOf<Element>, baseDelay: number = 100) => {
    elements.forEach((element, index) => {
      (element as HTMLElement).style.animationDelay = `${index * baseDelay}ms`;
      element.classList.add('slide-up-enter');
    });
  },

  /**
   * Triggers a data update animation
   */
  triggerDataUpdate: (element: HTMLElement) => {
    animationUtils.addTemporaryClass(element, 'pulse-data', 800);
  },

  /**
   * Creates a smooth transition between states
   */
  smoothTransition: (element: HTMLElement, fromClass: string, toClass: string) => {
    element.classList.remove(fromClass);
    element.classList.add('transition-all', 'duration-300');
    requestAnimationFrame(() => {
      element.classList.add(toClass);
    });
  },
};

/**
 * Performance-optimized animation presets
 */
export const performanceAnimations = {
  /**
   * GPU-accelerated transforms
   */
  gpuAccelerated: 'transform-gpu will-change-transform',

  /**
   * Reduced motion for accessibility
   */
  reducedMotion: 'motion-reduce:animate-none motion-reduce:transition-none',

  /**
   * High-performance hover effects
   */
  performantHover: 'transition-transform duration-200 ease-out hover:scale-105 transform-gpu',
};

/**
 * Theme-specific animation configurations
 */
export const themeAnimations = {
  degen: {
    primary: 'glow-green breathe-effect',
    secondary: 'glow-blue shimmer-effect',
    accent: 'glow-purple pulse-data',
    warning: 'glow-gold',
    danger: 'glow-red',
  },
  
  sophisticated: {
    subtle: 'hover-lift transition-colors-smooth',
    elegant: 'shimmer-effect fade-in-enter',
    premium: 'glow-gold breathe-effect',
  },
};

export default {
  ANIMATION_DURATIONS,
  ANIMATION_EASINGS,
  createAnimationClasses,
  priceChangeAnimation,
  interactiveAnimations,
  layoutAnimations,
  animationUtils,
  performanceAnimations,
  themeAnimations,
};
