'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { cn } from '@/lib/utils';

import { PulseDot, ShimmerText } from '@/components/ui/LiveIndicator';
import Icon from '@/components/ui/Icon';

interface SidebarProps {
  className?: string;
  isOpen?: boolean;
  onClose?: () => void;
}

export function Sidebar({ className, isOpen = true, onClose }: SidebarProps) {
  const pathname = usePathname();

  const navigation = [
    {
      name: 'Command Center',
      href: '/',
      icon: 'dashboard',
      color: 'text-degen-blue',
      gradient: 'from-degen-blue to-degen-purple',
    },
    {
      name: 'Live Alpha',
      href: '/signals',
      icon: 'signals',
      color: 'text-degen-purple',
      gradient: 'from-degen-purple to-degen-blue',
    },
    {
      name: 'My Bag',
      href: '/portfolio',
      icon: 'portfolio',
      color: 'text-degen-green',
      gradient: 'from-degen-green to-degen-gold',
    },
    {
      name: 'Diamond Hands',
      href: '/trading',
      icon: 'trading',
      color: 'text-degen-gold',
      gradient: 'from-degen-gold to-degen-green',
    },
    {
      name: 'Trade History',
      href: '/history',
      icon: 'chartUp',
      color: 'text-degen-blue',
      gradient: 'from-degen-blue to-degen-purple',
    },
    {
      name: 'Big Brain Stats',
      href: '/analytics',
      icon: 'analytics',
      color: 'text-degen-purple',
      gradient: 'from-degen-purple to-degen-gold',
    },
  ];

  const secondaryNavigation = [
    {
      name: 'Settings',
      href: '/settings',
      icon: 'settings',
      color: 'text-degen-gray',
      gradient: 'from-degen-gray to-degen-blue',
    },
    {
      name: 'Support',
      href: '/support',
      icon: 'target',
      color: 'text-degen-gray',
      gradient: 'from-degen-gray to-degen-purple',
    },
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <aside
      className={cn(
        'fixed inset-y-0 left-0 z-30 w-64 bg-gradient-degen border-r-2 border-degen-purple/30',
        'transform transition-all duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0',
        'backdrop-blur-sm shadow-2xl',
        isOpen ? 'translate-x-0' : '-translate-x-full',
        className
      )}
    >
      <div className="flex flex-col h-full relative">
        {/* Glow effect */}
        <div className="absolute inset-0 bg-gradient-to-b from-degen-purple/10 via-transparent to-degen-blue/10 pointer-events-none" />

        {/* Logo */}
        <div className="flex items-center h-16 px-6 border-b border-degen-purple/30 relative z-10">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="w-10 h-10 bg-gradient-to-r from-degen-green via-degen-blue to-degen-purple rounded-lg flex items-center justify-center glow-purple breathe-effect">
              <div className="w-6 h-6 bg-white rounded-full shimmer-effect"></div>
            </div>
            <ShimmerText speed="slow">
              <span className="text-xl font-bold bg-gradient-to-r from-degen-green via-degen-blue to-degen-purple bg-clip-text text-transparent">
                Signal V1
              </span>
            </ShimmerText>
            <PulseDot color="purple" size="sm" />
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto relative z-10">
          {navigation.map((item, index) => (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                'flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors-smooth hover-lift interactive-card',
                'border border-transparent',
                isActive(item.href)
                  ? `bg-gradient-to-r ${item.gradient} bg-opacity-20 ${item.color} border-gradient-${item.color.split('-')[1]} glow-${item.color.split('-')[1]} shadow-lg glow-pulse-effect`
                  : `text-degen-gray hover:${item.color} hover:bg-gradient-to-r hover:${item.gradient} hover:bg-opacity-10`
              )}
              onClick={onClose}
              style={{ animationDelay: `${index * 100}ms` }}
              suppressHydrationWarning
            >
              <div className="flex items-center">
                <Icon name={item.icon} size="md" color={isActive(item.href) ? item.color.split('-')[1] as any : 'gray'} className="mr-3" />
                {isActive(item.href) && <PulseDot color={item.color.split('-')[1] as any} size="sm" className="mr-2" />}
              </div>
              <span className="font-semibold tracking-wide">{item.name}</span>
            </Link>
          ))}
        </nav>

        {/* Secondary Navigation */}
        <div className="px-4 py-4 border-t border-degen-purple/30 relative z-10">
          <div className="space-y-2">
            {secondaryNavigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-colors-smooth hover-lift',
                  'border border-transparent',
                  isActive(item.href)
                    ? `bg-gradient-to-r ${item.gradient} bg-opacity-20 text-white glow-blue`
                    : `text-degen-gray hover:text-white hover:bg-gradient-to-r hover:${item.gradient} hover:bg-opacity-10`
                )}
                onClick={onClose}
                suppressHydrationWarning
              >
                <Icon name={item.icon} size="sm" color={isActive(item.href) ? 'white' : 'gray'} className="mr-3" />
                <span className="tracking-wide">{item.name}</span>
              </Link>
            ))}
          </div>
        </div>
      </div>
    </aside>
  );
}
