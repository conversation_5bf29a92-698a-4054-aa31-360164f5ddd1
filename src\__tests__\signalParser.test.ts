// Unit Tests for Signal Parser

import { SignalParser } from '../lib/telegram/signalParser';

describe('SignalParser', () => {
  describe('parseMessage', () => {
    it('should parse valid BONK signal', () => {
      const message = '🚨Alerted at $BONK - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 - Market Cap: $1.5B - This could be a gem! 🚀';
      const result = SignalParser.parseMessage(message, 'test-channel', 12345, new Date());

      expect(result).toBeTruthy();
      expect(result?.tokenAddress).toBe('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
      expect(result?.tokenSymbol).toBe('BONK');
      expect(result?.marketCap).toBe(1500000000);
      expect(result?.triggerPhrase).toBe('🚨Alerted at');
      expect(result?.confidence).toBeGreaterThan(0.5);
    });

    it('should parse valid SAMO signal', () => {
      const message = '🛎️First Hit on $SAMO - Contract: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU - MC: $50M - Early entry opportunity!';
      const result = SignalParser.parseMessage(message, 'test-channel', 12346, new Date());

      expect(result).toBeTruthy();
      expect(result?.tokenAddress).toBe('7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU');
      expect(result?.tokenSymbol).toBe('SAMO');
      expect(result?.marketCap).toBe(50000000);
      expect(result?.triggerPhrase).toBe('🛎️First Hit');
    });

    it('should parse ORCA signal with price', () => {
      const message = 'SIGNAL: $ORCA - Address: orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE - Price: $3.45 - Target: $10 🎯';
      const result = SignalParser.parseMessage(message, 'test-channel', 12347, new Date());

      expect(result).toBeTruthy();
      expect(result?.tokenAddress).toBe('orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE');
      expect(result?.tokenSymbol).toBe('ORCA');
      expect(result?.triggerPhrase).toBe('SIGNAL:');
    });

    it('should return null for invalid messages', () => {
      const invalidMessages = [
        'Just a regular message without signals',
        'Buy this amazing token now! 🚀', // No contract address
        '🚨Alerted at $FAKE - CA: invalid-address - This is fake',
      ];

      invalidMessages.forEach(message => {
        const result = SignalParser.parseMessage(message, 'test-channel', 12345, new Date());
        expect(result).toBeNull();
      });
    });
  });

  describe('extractTokenAddress', () => {
    it('should extract addresses with different prefixes', () => {
      const testCases = [
        {
          message: 'CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
          expected: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        },
        {
          message: 'Contract: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
          expected: '7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU',
        },
        {
          message: 'Address: orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
          expected: 'orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE',
        },
        {
          message: 'Token: So11111111111111111111111111111111111111112',
          expected: 'So11111111111111111111111111111111111111112',
        },
      ];

      testCases.forEach(({ message, expected }) => {
        const result = SignalParser.parseMessage(
          `🚨Alerted at $TEST - ${message}`,
          'test-channel',
          12345,
          new Date()
        );
        expect(result?.tokenAddress).toBe(expected);
      });
    });

    it('should handle multiple addresses and pick the first valid one', () => {
      const message = '🚨Alerted at $TEST - CA: invalid-address - Contract: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263';
      const result = SignalParser.parseMessage(message, 'test-channel', 12345, new Date());
      expect(result?.tokenAddress).toBe('DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263');
    });
  });

  describe('extractTokenSymbol', () => {
    it('should extract symbols with different formats', () => {
      const testCases = [
        { message: '$BONK is going to moon', expected: 'BONK' },
        { message: 'Symbol: SAMO looks good', expected: 'SAMO' },
        { message: 'Check out (ORCA) token', expected: 'ORCA' },
        { message: 'Ticker: SOL is solid', expected: 'SOL' },
      ];

      testCases.forEach(({ message, expected }) => {
        const result = SignalParser.parseMessage(
          `🚨Alerted at ${message} - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263`,
          'test-channel',
          12345,
          new Date()
        );
        expect(result?.tokenSymbol).toBe(expected);
      });
    });

    it('should handle invalid symbols', () => {
      const invalidMessages = [
        '$A', // Too short
        '$VERYLONGSYMBOL', // Too long
        'No symbol here',
      ];

      invalidMessages.forEach(message => {
        const result = SignalParser.parseMessage(
          `🚨Alerted at ${message} - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263`,
          'test-channel',
          12345,
          new Date()
        );
        expect(result?.tokenSymbol).toBeNull();
      });
    });
  });

  describe('extractMarketCap', () => {
    it('should extract market cap with different formats', () => {
      const testCases = [
        { message: 'Market Cap: $1.5M', expected: 1500000 },
        { message: 'MC: $50M', expected: 50000000 },
        { message: '$2.5B MC', expected: 2500000000 },
        { message: 'Cap: $100K', expected: 100000 },
        { message: 'Market Cap: 1.5M', expected: 1500000 }, // Without $
      ];

      testCases.forEach(({ message, expected }) => {
        const result = SignalParser.parseMessage(
          `🚨Alerted at $TEST - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 - ${message}`,
          'test-channel',
          12345,
          new Date()
        );
        expect(result?.marketCap).toBe(expected);
      });
    });

    it('should handle invalid market cap formats', () => {
      const invalidMessages = [
        'No market cap here',
        'Market Cap: invalid',
        'MC: $',
      ];

      invalidMessages.forEach(message => {
        const result = SignalParser.parseMessage(
          `🚨Alerted at $TEST - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 - ${message}`,
          'test-channel',
          12345,
          new Date()
        );
        expect(result?.marketCap).toBeNull();
      });
    });
  });

  describe('calculateConfidence', () => {
    it('should give higher confidence to strong signals', () => {
      const strongMessage = '🚨Alerted at $BONK - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 - Market Cap: $1.5B - BUY NOW! Target x10 🚀';
      const weakMessage = 'Maybe check out this token - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263';

      const strongResult = SignalParser.parseMessage(strongMessage, 'test-channel', 12345, new Date());
      const weakResult = SignalParser.parseMessage(weakMessage, 'test-channel', 12346, new Date());

      expect(strongResult?.confidence).toBeGreaterThan(weakResult?.confidence || 0);
    });

    it('should cap confidence at 1.0', () => {
      const message = '🚨Alerted at $BONK - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 - BUY NOW entry target moon gem x100';
      const result = SignalParser.parseMessage(message, 'test-channel', 12345, new Date());
      expect(result?.confidence).toBeLessThanOrEqual(1.0);
    });
  });

  describe('extractPriceInfo', () => {
    it('should extract price information', () => {
      const testCases = [
        { message: 'Price: $3.45', expectedPrice: 3.45 },
        { message: '$2.50 USD', expectedPrice: 2.50 },
        { message: 'Current: $1.25', expectedPrice: 1.25 },
        { message: 'Price: $3.45 (+5.2%)', expectedPrice: 3.45, expectedChange: 5.2 },
        { message: 'Change: +10.5%', expectedChange: 10.5 },
        { message: '24h: -2.3%', expectedChange: -2.3 },
      ];

      testCases.forEach(({ message, expectedPrice, expectedChange }) => {
        const result = SignalParser.extractPriceInfo(message);
        
        if (expectedPrice) {
          expect(result.price).toBe(expectedPrice);
        }
        
        if (expectedChange) {
          expect(result.priceChange).toBe(expectedChange);
        }
      });
    });
  });

  describe('isLikelyScam', () => {
    it('should detect scam indicators', () => {
      const scamMessages = [
        'Guaranteed 100% profit!',
        'Risk free investment opportunity',
        'Send me money for signals',
        'Private message me for details',
        'Limited time offer - act fast!',
        "Don't miss this last chance",
      ];

      scamMessages.forEach(message => {
        expect(SignalParser.isLikelyScam(message)).toBe(true);
      });
    });

    it('should not flag legitimate messages', () => {
      const legitimateMessages = [
        '🚨Alerted at $BONK - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        'New signal detected for SAMO token',
        'Market analysis shows potential upside',
      ];

      legitimateMessages.forEach(message => {
        expect(SignalParser.isLikelyScam(message)).toBe(false);
      });
    });
  });

  describe('validateSignal', () => {
    it('should validate good signals', () => {
      const goodSignal = {
        tokenAddress: 'DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263',
        tokenSymbol: 'BONK',
        marketCap: 1500000000,
        triggerPhrase: '🚨Alerted at',
        confidence: 0.8,
        metadata: {
          channelId: 'test-channel',
          messageId: 12345,
          timestamp: new Date(),
        },
      };

      const result = SignalParser.validateSignal(goodSignal);
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should reject invalid signals', () => {
      const invalidSignal = {
        tokenAddress: 'invalid-address',
        tokenSymbol: 'BONK',
        marketCap: 1500000000,
        triggerPhrase: 'guaranteed profit',
        confidence: 0.3,
        metadata: {
          channelId: 'test-channel',
          messageId: 12345,
          timestamp: new Date(),
        },
      };

      const result = SignalParser.validateSignal(invalidSignal);
      expect(result.valid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });
});

// Mock the constants
jest.mock('../lib/constants', () => ({
  TELEGRAM_CONFIG: {
    signalTriggers: ['🚨Alerted at', '🛎️First Hit', 'SIGNAL:', 'BUY:', 'ENTRY:'],
  },
  VALIDATION_RULES: {
    solanaAddress: {
      length: 44,
      pattern: /^[1-9A-HJ-NP-Za-km-z]{44}$/,
    },
  },
}));
