'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useWallet } from '@/hooks/useWallet';
import { WalletConnectionModal } from '@/components/wallet/WalletConnectionModal';
import { Button } from '@/components/ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { PulseDot, ShimmerText } from '@/components/ui/LiveIndicator';
import { DEGEN_EMOJIS } from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';

interface WalletAuthGateProps {
  children: React.ReactNode;
  requireWallet?: boolean;
  redirectTo?: string;
}

// Routes that don't require wallet connection
const PUBLIC_ROUTES = [
  '/',
  '/welcome',
  '/login',
  '/about',
  '/terms',
  '/privacy',
];

// Routes that require wallet connection
const PROTECTED_ROUTES = [
  '/signals',
  '/portfolio',
  '/trading',
  '/analytics',
  '/history',
  '/settings',
  '/support',
];

export function WalletAuthGate({ 
  children, 
  requireWallet = true,
  redirectTo = '/'
}: WalletAuthGateProps) {
  const { connected, connecting, isRestoring } = useWallet();
  const router = useRouter();
  const pathname = usePathname();
  const [showConnectionModal, setShowConnectionModal] = useState(false);
  const [hasCheckedAuth, setHasCheckedAuth] = useState(false);

  // Check if current route requires authentication
  const isProtectedRoute = PROTECTED_ROUTES.some(route => pathname.startsWith(route));
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname);

  useEffect(() => {
    // Wait for wallet restoration to complete
    if (isRestoring) {
      return;
    }

    setHasCheckedAuth(true);

    // If this is a protected route and wallet is not connected
    if (isProtectedRoute && !connected && requireWallet) {
      console.log(`🔒 Protected route ${pathname} requires wallet connection`);
      // Don't redirect immediately, show connection screen instead
      return;
    }

    // If wallet is connected and user is on a public route, they can stay
    if (connected && isPublicRoute) {
      console.log(`✅ Wallet connected, allowing access to ${pathname}`);
      return;
    }

  }, [connected, isRestoring, pathname, isProtectedRoute, isPublicRoute, requireWallet]);

  // Show loading state while checking authentication
  if (!hasCheckedAuth || isRestoring) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <ShimmerText speed="normal">
            <p className="text-white text-lg">Initializing Signal V1...</p>
          </ShimmerText>
          <p className="text-gray-400 text-sm mt-2">
            {isRestoring ? 'Restoring wallet connection...' : 'Loading application...'}
          </p>
        </div>
      </div>
    );
  }

  // If this is a protected route and wallet is not connected, show connection screen
  if (isProtectedRoute && !connected && requireWallet) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="text-6xl mb-4 animate-float">
              {DEGEN_EMOJIS.diamond}
            </div>
            <CardTitle className="text-2xl font-bold text-green-400 mb-2">
              Wallet Required
            </CardTitle>
            <p className="text-gray-400">
              Connect your Solana wallet to access Signal V1 features
            </p>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="bg-gray-800/50 border border-gray-700/50 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-white mb-3 flex items-center gap-2">
                <Icon name="signals" className="w-5 h-5 text-green-400" />
                Protected Features
              </h3>
              <ul className="space-y-2 text-sm text-gray-300">
                <li className="flex items-center gap-2">
                  <PulseDot color="green" size="sm" />
                  Live Alpha Signals
                </li>
                <li className="flex items-center gap-2">
                  <PulseDot color="green" size="sm" />
                  Portfolio Management
                </li>
                <li className="flex items-center gap-2">
                  <PulseDot color="green" size="sm" />
                  Automated Trading
                </li>
                <li className="flex items-center gap-2">
                  <PulseDot color="green" size="sm" />
                  Analytics & History
                </li>
              </ul>
            </div>

            <div className="space-y-3">
              <Button
                onClick={() => setShowConnectionModal(true)}
                className="w-full"
                size="lg"
                disabled={connecting}
              >
                {connecting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Connecting...
                  </>
                ) : (
                  <>
                    <Icon name="wallet" className="w-4 h-4 mr-2" />
                    Connect Wallet
                  </>
                )}
              </Button>

              <Button
                onClick={() => router.push('/')}
                variant="outline"
                className="w-full"
                size="lg"
              >
                <Icon name="home" className="w-4 h-4 mr-2" />
                Back to Home
              </Button>
            </div>

            <div className="text-center">
              <p className="text-xs text-gray-500">
                Supported wallets: Phantom, Solflare, Backpack, Glow
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Wallet Connection Modal */}
        <WalletConnectionModal
          isOpen={showConnectionModal}
          onClose={() => setShowConnectionModal(false)}
          onConnect={() => {
            setShowConnectionModal(false);
            // Connection success will be handled by the useEffect
          }}
        />
      </div>
    );
  }

  // If wallet is connected or route doesn't require wallet, render children
  return <>{children}</>;
}

// Higher-order component for easy wrapping
export function withWalletAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: { requireWallet?: boolean; redirectTo?: string } = {}
) {
  return function WrappedComponent(props: P) {
    return (
      <WalletAuthGate {...options}>
        <Component {...props} />
      </WalletAuthGate>
    );
  };
}
