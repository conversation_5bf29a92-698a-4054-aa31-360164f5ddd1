'use client';

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { useWallet } from '@solana/wallet-adapter-react';
import { Button } from '@/components/ui/Button';
import { SUPPORTED_WALLETS, getAvailableWallets } from '@/lib/wallet/config';
import { cn } from '@/lib/utils';

interface WalletConnectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: () => void;
}

export function WalletConnectionModal({ isOpen, onClose, onConnect }: WalletConnectionModalProps) {
  const { select, connect, connecting, connected } = useWallet();
  const [availableWallets, setAvailableWallets] = useState<typeof SUPPORTED_WALLETS>([]);
  const [error, setError] = useState<string | null>(null);
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    // Mark as client-side and get available wallets
    setIsClient(true);
    if (typeof window !== 'undefined') {
      try {
        const wallets = getAvailableWallets();
        setAvailableWallets(wallets);
      } catch (err) {
        console.error('Error getting available wallets:', err);
        setAvailableWallets([]);
      }
    }
  }, []);

  useEffect(() => {
    if (connected) {
      onConnect();
      onClose();
    }
  }, [connected, onConnect, onClose]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key
  useEffect(() => {
    if (!isOpen) return;

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => document.removeEventListener('keydown', handleEscape);
  }, [isOpen, onClose]);

  const handleWalletSelect = async (walletName: string) => {
    try {
      setError(null);
      
      // Select the wallet adapter
      select(walletName);
      
      // Small delay to ensure wallet is selected
      setTimeout(async () => {
        try {
          await connect();
        } catch (err) {
          console.error('Connection error:', err);
          if (err instanceof Error) {
            if (err.name === 'WalletNotReadyError') {
              setError(`${walletName} wallet is not ready. Please ensure it's installed and unlocked.`);
            } else if (err.message.includes('User rejected')) {
              setError('Connection cancelled by user.');
            } else {
              setError(err.message || 'Failed to connect wallet');
            }
          }
        }
      }, 100);
    } catch (err) {
      console.error('Wallet selection error:', err);
      setError('Failed to select wallet');
    }
  };

  if (!isOpen) return null;

  const modalContent = (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-[9999] p-4"
      onClick={(e) => {
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div className="bg-gray-900/95 border border-gray-700/50 rounded-2xl p-6 max-w-md w-full shadow-2xl backdrop-blur-md max-h-[90vh] overflow-y-auto mx-auto my-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Connect Wallet</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors p-1 hover:bg-gray-800 rounded-lg"
            aria-label="Close modal"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-900/50 border border-red-500/50 rounded-lg">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}

        <div className="space-y-3">
          {!isClient ? (
            // Loading state during SSR/hydration
            <div className="text-center py-8">
              <div className="text-4xl mb-4">⏳</div>
              <p className="text-gray-400">Detecting wallets...</p>
            </div>
          ) : availableWallets.length > 0 ? (
            availableWallets.map((wallet) => (
              <Button
                key={wallet.name}
                onClick={() => handleWalletSelect(wallet.name)}
                disabled={connecting}
                variant="outline"
                className={cn(
                  'w-full flex items-center justify-start gap-3 p-4 h-auto min-h-[60px]',
                  'border-gray-600 hover:border-green-500 hover:bg-green-500/10',
                  'transition-all duration-200 text-left'
                )}
              >
                <div className="w-8 h-8 bg-gray-700 rounded-lg flex items-center justify-center">
                  <span className="text-lg">🔗</span>
                </div>
                <div className="flex-1 text-left">
                  <div className="font-medium text-white">{wallet.name}</div>
                  <div className="text-sm text-gray-400">
                    {connecting ? 'Connecting...' : 'Click to connect'}
                  </div>
                </div>
              </Button>
            ))
          ) : (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">🔌</div>
              <h3 className="text-lg font-medium text-white mb-2">No Wallets Detected</h3>
              <p className="text-gray-400 text-sm mb-4">
                Please install a supported Solana wallet to continue
              </p>
              <div className="space-y-2">
                {SUPPORTED_WALLETS.map((wallet) => (
                  <a
                    key={wallet.name}
                    href={wallet.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block text-green-400 hover:text-green-300 text-sm underline"
                  >
                    Install {wallet.name}
                  </a>
                ))}
              </div>
            </div>
          )}
        </div>

        <div className="mt-6 pt-4 border-t border-gray-700/50">
          <p className="text-xs text-gray-500 text-center leading-relaxed">
            By connecting your wallet, you agree to our Terms of Service and Privacy Policy
          </p>
        </div>
      </div>
    </div>
  );

  // Render modal in portal to ensure it's at the top level
  return typeof window !== 'undefined'
    ? createPortal(modalContent, document.body)
    : null;
}
