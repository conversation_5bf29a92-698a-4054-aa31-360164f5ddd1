'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { SignalParser } from '@/lib/telegram/signalParser';
import type { ParsedSignal } from '@/types';

interface SignalTesterProps {
  className?: string;
}

export function SignalTester({ className }: SignalTesterProps) {
  const [testMessage, setTestMessage] = useState('');
  const [parsedSignal, setParsedSignal] = useState<ParsedSignal | null>(null);
  const [isValid, setIsValid] = useState<boolean | null>(null);
  const [errors, setErrors] = useState<string[]>([]);

  // Sample test messages
  const sampleMessages = [
    {
      name: 'Valid BONK Signal',
      content: '🚨Alerted at $BONK - CA: DezXAZ8z7PnrnRJjz3wXBoRgixCa6xjnB7YaB1pPB263 - Market Cap: $1.5B - This could be a gem! 🚀',
    },
    {
      name: 'Valid SAMO Signal',
      content: '🛎️First Hit on $SAMO - Contract: 7xKXtg2CW87d97TXJSDpbD5jBkheTqA83TZRuJosgAsU - MC: $50M - Early entry opportunity!',
    },
    {
      name: 'ORCA Signal with Price',
      content: 'SIGNAL: $ORCA - Address: orcaEKTdK7LKz57vaAYr9QeNsVEPfiu6QeMU1kektZE - Price: $3.45 - Target: $10 🎯',
    },
    {
      name: 'Invalid Signal (No CA)',
      content: 'Buy this amazing token now! Guaranteed 100x returns! 🚀🚀🚀',
    },
    {
      name: 'SOL Signal',
      content: 'BUY: $SOL - CA: So11111111111111111111111111111111111111112 - Current: $98.45 - +5.2% today',
    },
  ];

  const handleParseMessage = () => {
    if (!testMessage.trim()) {
      setParsedSignal(null);
      setIsValid(null);
      setErrors([]);
      return;
    }

    try {
      // Parse the message
      const signal = SignalParser.parseMessage(
        testMessage,
        'test-channel',
        12345,
        new Date()
      );

      setParsedSignal(signal);

      if (signal) {
        // Validate the signal
        const validation = SignalParser.validateSignal(signal);
        setIsValid(validation.valid);
        setErrors(validation.errors);
      } else {
        setIsValid(false);
        setErrors(['No valid signal found in message']);
      }
    } catch (error) {
      console.error('Error parsing signal:', error);
      setParsedSignal(null);
      setIsValid(false);
      setErrors(['Error parsing message']);
    }
  };

  const loadSampleMessage = (message: string) => {
    setTestMessage(message);
    // Auto-parse when loading sample
    setTimeout(() => {
      const signal = SignalParser.parseMessage(
        message,
        'test-channel',
        12345,
        new Date()
      );

      setParsedSignal(signal);

      if (signal) {
        const validation = SignalParser.validateSignal(signal);
        setIsValid(validation.valid);
        setErrors(validation.errors);
      } else {
        setIsValid(false);
        setErrors(['No valid signal found in message']);
      }
    }, 100);
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle>Signal Parser Tester</CardTitle>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Test the Telegram signal parsing functionality with sample messages
          </p>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Sample Messages */}
          <div>
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
              Sample Messages
            </h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
              {sampleMessages.map((sample, index) => (
                <Button
                  key={index}
                  variant="outline"
                  size="sm"
                  onClick={() => loadSampleMessage(sample.content)}
                  className="text-left justify-start"
                >
                  {sample.name}
                </Button>
              ))}
            </div>
          </div>

          {/* Message Input */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Test Message
            </label>
            <textarea
              className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              placeholder="Enter a Telegram message to parse..."
              value={testMessage}
              onChange={(e) => setTestMessage(e.target.value)}
            />
          </div>

          {/* Parse Button */}
          <Button onClick={handleParseMessage} className="w-full">
            Parse Signal
          </Button>

          {/* Results */}
          {(parsedSignal || isValid === false) && (
            <div className="space-y-4">
              {/* Validation Status */}
              <div className="flex items-center space-x-2">
                <Badge variant={isValid ? 'success' : 'error'}>
                  {isValid ? 'Valid Signal' : 'Invalid Signal'}
                </Badge>
                {parsedSignal && (
                  <Badge variant="secondary">
                    Confidence: {(parsedSignal.confidence * 100).toFixed(1)}%
                  </Badge>
                )}
              </div>

              {/* Errors */}
              {errors.length > 0 && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3">
                  <h5 className="text-sm font-medium text-red-800 dark:text-red-400 mb-2">
                    Validation Errors:
                  </h5>
                  <ul className="text-sm text-red-700 dark:text-red-300 space-y-1">
                    {errors.map((error, index) => (
                      <li key={index}>• {error}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* Parsed Signal Details */}
              {parsedSignal && (
                <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
                  <h5 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
                    Parsed Signal Details:
                  </h5>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Token Address:</span>
                      <p className="font-mono text-xs break-all mt-1">
                        {parsedSignal.tokenAddress}
                      </p>
                    </div>
                    {parsedSignal.tokenSymbol && (
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Token Symbol:</span>
                        <p className="font-medium mt-1">{parsedSignal.tokenSymbol}</p>
                      </div>
                    )}
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Trigger Phrase:</span>
                      <p className="font-medium mt-1">{parsedSignal.triggerPhrase}</p>
                    </div>
                    <div>
                      <span className="text-gray-600 dark:text-gray-400">Confidence:</span>
                      <p className="font-medium mt-1">
                        {(parsedSignal.confidence * 100).toFixed(1)}%
                      </p>
                    </div>
                    {parsedSignal.marketCap && (
                      <div>
                        <span className="text-gray-600 dark:text-gray-400">Market Cap:</span>
                        <p className="font-medium mt-1">
                          ${parsedSignal.marketCap.toLocaleString()}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
