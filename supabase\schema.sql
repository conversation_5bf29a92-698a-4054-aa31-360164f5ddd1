-- Signal V1 Database Schema
-- This file contains the complete database schema for the Signal V1 trading application

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create custom types
CREATE TYPE trade_type AS ENUM ('buy', 'sell');
CREATE TYPE trade_status AS ENUM ('pending', 'confirmed', 'failed');
CREATE TYPE position_status AS ENUM ('active', 'closed');
CREATE TYPE support_category AS ENUM ('technical', 'trading', 'account', 'feature_request', 'general');
CREATE TYPE support_priority AS ENUM ('low', 'medium', 'high', 'critical');
CREATE TYPE support_status AS ENUM ('open', 'in_progress', 'resolved', 'closed');

-- Users table (extends Supabase auth.users)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    wallet_address TEXT UNIQUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    preferences JSONB DEFAULT '{
        "theme": "system",
        "notifications": {
            "signals": true,
            "trades": true,
            "priceAlerts": true,
            "portfolio": true,
            "push": true,
            "email": false
        },
        "trading": {
            "defaultAmount": 0.1,
            "maxPositionPercentage": 5,
            "stopLossPercentage": 30,
            "slippageTolerance": 3,
            "autoTrade": false,
            "maxActivePositions": 10,
            "profitTakingEnabled": true
        },
        "display": {
            "currency": "USD",
            "language": "en",
            "timezone": "UTC",
            "compactMode": false
        }
    }'::jsonb
);

-- Telegram channels table
CREATE TABLE telegram_channels (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    username TEXT,
    description TEXT,
    member_count INTEGER,
    active BOOLEAN DEFAULT true,
    last_signal TIMESTAMP WITH TIME ZONE,
    signal_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Telegram signals table
CREATE TABLE telegram_signals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    channel_id UUID REFERENCES telegram_channels(id) ON DELETE CASCADE,
    channel_name TEXT NOT NULL,
    message_id BIGINT NOT NULL,
    content TEXT NOT NULL,
    token_address TEXT,
    token_symbol TEXT,
    market_cap NUMERIC,
    images TEXT[],
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    processed BOOLEAN DEFAULT false,
    valid BOOLEAN DEFAULT false,
    error TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(channel_id, message_id)
);

-- Portfolios table
CREATE TABLE portfolios (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    total_value NUMERIC DEFAULT 0,
    total_pnl NUMERIC DEFAULT 0,
    total_pnl_percentage NUMERIC DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Positions table
CREATE TABLE positions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    token_address TEXT NOT NULL,
    token_symbol TEXT NOT NULL,
    token_name TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    entry_price NUMERIC NOT NULL,
    current_price NUMERIC NOT NULL,
    entry_value NUMERIC NOT NULL,
    current_value NUMERIC NOT NULL,
    pnl NUMERIC NOT NULL,
    pnl_percentage NUMERIC NOT NULL,
    multiplier NUMERIC NOT NULL,
    status position_status DEFAULT 'active',
    entry_date TIMESTAMP WITH TIME ZONE NOT NULL,
    exit_date TIMESTAMP WITH TIME ZONE,
    signal_id UUID REFERENCES telegram_signals(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trades table
CREATE TABLE trades (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    portfolio_id UUID REFERENCES portfolios(id) ON DELETE CASCADE,
    position_id UUID REFERENCES positions(id),
    type trade_type NOT NULL,
    token_address TEXT NOT NULL,
    token_symbol TEXT NOT NULL,
    amount NUMERIC NOT NULL,
    price NUMERIC NOT NULL,
    value NUMERIC NOT NULL,
    slippage NUMERIC NOT NULL,
    fees NUMERIC NOT NULL,
    signature TEXT NOT NULL UNIQUE,
    status trade_status DEFAULT 'pending',
    timestamp TIMESTAMP WITH TIME ZONE NOT NULL,
    signal_id UUID REFERENCES telegram_signals(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Support tickets table
CREATE TABLE support_tickets (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    category support_category NOT NULL,
    priority support_priority NOT NULL DEFAULT 'medium',
    status support_status NOT NULL DEFAULT 'open',
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    ticket_number TEXT NOT NULL UNIQUE,
    admin_notes TEXT,
    resolved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_wallet_address ON users(wallet_address);
CREATE INDEX idx_telegram_signals_channel_id ON telegram_signals(channel_id);
CREATE INDEX idx_telegram_signals_timestamp ON telegram_signals(timestamp DESC);
CREATE INDEX idx_telegram_signals_token_address ON telegram_signals(token_address);
CREATE INDEX idx_portfolios_user_id ON portfolios(user_id);
CREATE INDEX idx_positions_portfolio_id ON positions(portfolio_id);
CREATE INDEX idx_positions_status ON positions(status);
CREATE INDEX idx_positions_token_address ON positions(token_address);
CREATE INDEX idx_trades_portfolio_id ON trades(portfolio_id);
CREATE INDEX idx_trades_position_id ON trades(position_id);
CREATE INDEX idx_trades_timestamp ON trades(timestamp DESC);
CREATE INDEX idx_trades_signature ON trades(signature);
CREATE INDEX idx_support_tickets_user_id ON support_tickets(user_id);
CREATE INDEX idx_support_tickets_status ON support_tickets(status);
CREATE INDEX idx_support_tickets_category ON support_tickets(category);
CREATE INDEX idx_support_tickets_priority ON support_tickets(priority);
CREATE INDEX idx_support_tickets_created_at ON support_tickets(created_at DESC);
CREATE INDEX idx_support_tickets_ticket_number ON support_tickets(ticket_number);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at columns
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_telegram_channels_updated_at BEFORE UPDATE ON telegram_channels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_portfolios_updated_at BEFORE UPDATE ON portfolios
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at BEFORE UPDATE ON positions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_support_tickets_updated_at BEFORE UPDATE ON support_tickets
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE portfolios ENABLE ROW LEVEL SECURITY;
ALTER TABLE positions ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_tickets ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Portfolio policies
CREATE POLICY "Users can view own portfolio" ON portfolios
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own portfolio" ON portfolios
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own portfolio" ON portfolios
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Position policies
CREATE POLICY "Users can view own positions" ON positions
    FOR SELECT USING (
        portfolio_id IN (
            SELECT id FROM portfolios WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage own positions" ON positions
    FOR ALL USING (
        portfolio_id IN (
            SELECT id FROM portfolios WHERE user_id = auth.uid()
        )
    );

-- Trade policies
CREATE POLICY "Users can view own trades" ON trades
    FOR SELECT USING (
        portfolio_id IN (
            SELECT id FROM portfolios WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert own trades" ON trades
    FOR INSERT WITH CHECK (
        portfolio_id IN (
            SELECT id FROM portfolios WHERE user_id = auth.uid()
        )
    );

-- Support tickets policies
CREATE POLICY "Users can view own support tickets" ON support_tickets
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create support tickets" ON support_tickets
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own support tickets" ON support_tickets
    FOR UPDATE USING (auth.uid() = user_id);

-- Public read access for telegram data (signals and channels)
CREATE POLICY "Public read access for telegram_channels" ON telegram_channels
    FOR SELECT USING (true);

CREATE POLICY "Public read access for telegram_signals" ON telegram_signals
    FOR SELECT USING (true);

-- Function to create user profile and portfolio on signup
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO users (id, email)
    VALUES (NEW.id, NEW.email);
    
    INSERT INTO portfolios (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to automatically create user profile and portfolio
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Function to increment channel signal count
CREATE OR REPLACE FUNCTION increment_channel_signal_count(
    channel_id UUID,
    last_signal_time TIMESTAMP WITH TIME ZONE
)
RETURNS VOID AS $$
BEGIN
    UPDATE telegram_channels
    SET
        signal_count = signal_count + 1,
        last_signal = last_signal_time,
        updated_at = NOW()
    WHERE id = channel_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to calculate portfolio totals
CREATE OR REPLACE FUNCTION calculate_portfolio_totals(portfolio_id UUID)
RETURNS TABLE(
    total_value NUMERIC,
    total_pnl NUMERIC,
    total_pnl_percentage NUMERIC
) AS $$
DECLARE
    total_val NUMERIC := 0;
    total_profit_loss NUMERIC := 0;
    total_entry_value NUMERIC := 0;
BEGIN
    -- Calculate totals from active positions
    SELECT
        COALESCE(SUM(current_value), 0),
        COALESCE(SUM(pnl), 0),
        COALESCE(SUM(entry_value), 0)
    INTO total_val, total_profit_loss, total_entry_value
    FROM positions
    WHERE portfolio_id = calculate_portfolio_totals.portfolio_id
    AND status = 'active';

    -- Calculate percentage
    total_pnl_percentage := CASE
        WHEN total_entry_value > 0 THEN (total_profit_loss / total_entry_value) * 100
        ELSE 0
    END;

    RETURN QUERY SELECT total_val, total_profit_loss, total_pnl_percentage;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
