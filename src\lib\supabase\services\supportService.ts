// Support Service - Handle support ticket operations

import { supabase } from '../client';
import type { Database } from '../types';

type SupportTicket = Database['public']['Tables']['support_tickets']['Row'];
type SupportTicketInsert = Database['public']['Tables']['support_tickets']['Insert'];
type SupportTicketUpdate = Database['public']['Tables']['support_tickets']['Update'];

export class SupportService {
  /**
   * Generate a unique ticket number
   */
  static generateTicketNumber(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `SV1-${timestamp}-${random}`.toUpperCase();
  }

  /**
   * Create a new support ticket
   */
  static async createTicket(ticketData: Omit<SupportTicketInsert, 'ticket_number'>): Promise<{
    data: SupportTicket | null;
    error: string | null;
  }> {
    try {
      const ticketNumber = this.generateTicketNumber();
      
      const { data, error } = await supabase
        .from('support_tickets')
        .insert({
          ...ticketData,
          ticket_number: ticketNumber,
          status: 'open',
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating support ticket:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error creating support ticket:', error);
      return { data: null, error: 'Failed to create support ticket' };
    }
  }

  /**
   * Get all tickets for a user
   */
  static async getUserTickets(userId: string): Promise<{
    data: SupportTicket[] | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching user tickets:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error fetching user tickets:', error);
      return { data: null, error: 'Failed to fetch tickets' };
    }
  }

  /**
   * Get a specific ticket by ID
   */
  static async getTicketById(ticketId: string): Promise<{
    data: SupportTicket | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .select('*')
        .eq('id', ticketId)
        .single();

      if (error) {
        console.error('Error fetching ticket:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error fetching ticket:', error);
      return { data: null, error: 'Failed to fetch ticket' };
    }
  }

  /**
   * Update a support ticket
   */
  static async updateTicket(ticketId: string, updates: SupportTicketUpdate): Promise<{
    data: SupportTicket | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', ticketId)
        .select()
        .single();

      if (error) {
        console.error('Error updating ticket:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error updating ticket:', error);
      return { data: null, error: 'Failed to update ticket' };
    }
  }

  /**
   * Close a support ticket
   */
  static async closeTicket(ticketId: string): Promise<{
    data: SupportTicket | null;
    error: string | null;
  }> {
    return this.updateTicket(ticketId, {
      status: 'closed',
      resolved_at: new Date().toISOString(),
    });
  }

  /**
   * Get ticket statistics for a user
   */
  static async getTicketStats(userId: string): Promise<{
    data: {
      total: number;
      open: number;
      in_progress: number;
      resolved: number;
      closed: number;
    } | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .select('status')
        .eq('user_id', userId);

      if (error) {
        console.error('Error fetching ticket stats:', error);
        return { data: null, error: error.message };
      }

      const stats = {
        total: data.length,
        open: data.filter(t => t.status === 'open').length,
        in_progress: data.filter(t => t.status === 'in_progress').length,
        resolved: data.filter(t => t.status === 'resolved').length,
        closed: data.filter(t => t.status === 'closed').length,
      };

      return { data: stats, error: null };
    } catch (error) {
      console.error('Error fetching ticket stats:', error);
      return { data: null, error: 'Failed to fetch ticket statistics' };
    }
  }

  /**
   * Search tickets by content
   */
  static async searchTickets(userId: string, query: string): Promise<{
    data: SupportTicket[] | null;
    error: string | null;
  }> {
    try {
      const { data, error } = await supabase
        .from('support_tickets')
        .select('*')
        .eq('user_id', userId)
        .or(`title.ilike.%${query}%,description.ilike.%${query}%,ticket_number.ilike.%${query}%`)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error searching tickets:', error);
        return { data: null, error: error.message };
      }

      return { data, error: null };
    } catch (error) {
      console.error('Error searching tickets:', error);
      return { data: null, error: 'Failed to search tickets' };
    }
  }
}
