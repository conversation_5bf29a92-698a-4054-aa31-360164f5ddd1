// Portfolio Service - Handles portfolio and trading data operations

import { supabase } from '../client';
import type { Portfolio, Position, Trade } from '@/types';

export class PortfolioService {
  /**
   * Get user's portfolio
   */
  static async getUserPortfolio(): Promise<Portfolio | null> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return null;
      }

      const { data, error } = await supabase
        .from('portfolios')
        .select(`
          *,
          positions(*),
          trades(*)
        `)
        .eq('user_id', user.id)
        .single();

      if (error) {
        console.error('Error fetching portfolio:', error);
        return null;
      }

      return {
        id: data.id,
        userId: data.user_id,
        totalValue: Number(data.total_value),
        totalPnL: Number(data.total_pnl),
        totalPnLPercentage: Number(data.total_pnl_percentage),
        positions: data.positions?.map(this.mapPosition) || [],
        trades: data.trades?.map(this.mapTrade) || [],
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
      };
    } catch (error) {
      console.error('Error in getUserPortfolio:', error);
      return null;
    }
  }

  /**
   * Get active positions
   */
  static async getActivePositions(): Promise<Position[]> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return [];
      }

      const { data, error } = await supabase
        .from('positions')
        .select(`
          *,
          portfolio:portfolios!inner(user_id)
        `)
        .eq('portfolio.user_id', user.id)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching active positions:', error);
        return [];
      }

      return data.map(this.mapPosition);
    } catch (error) {
      console.error('Error in getActivePositions:', error);
      return [];
    }
  }

  /**
   * Get recent trades
   */
  static async getRecentTrades(limit: number = 50): Promise<Trade[]> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return [];
      }

      const { data, error } = await supabase
        .from('trades')
        .select(`
          *,
          portfolio:portfolios!inner(user_id)
        `)
        .eq('portfolio.user_id', user.id)
        .order('timestamp', { ascending: false })
        .limit(limit);

      if (error) {
        console.error('Error fetching recent trades:', error);
        return [];
      }

      return data.map(this.mapTrade);
    } catch (error) {
      console.error('Error in getRecentTrades:', error);
      return [];
    }
  }

  /**
   * Create a new position
   */
  static async createPosition(position: Omit<Position, 'id' | 'createdAt' | 'updatedAt'>): Promise<Position | null> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return null;
      }

      // Get user's portfolio
      const { data: portfolio, error: portfolioError } = await supabase
        .from('portfolios')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (portfolioError || !portfolio) {
        console.error('Error fetching portfolio:', portfolioError);
        return null;
      }

      const { data, error } = await supabase
        .from('positions')
        .insert({
          portfolio_id: portfolio.id,
          token_address: position.tokenAddress,
          token_symbol: position.tokenSymbol,
          token_name: position.tokenName,
          amount: position.amount,
          entry_price: position.entryPrice,
          current_price: position.currentPrice,
          entry_value: position.entryValue,
          current_value: position.currentValue,
          pnl: position.pnl,
          pnl_percentage: position.pnlPercentage,
          multiplier: position.multiplier,
          status: position.status,
          entry_date: position.entryDate.toISOString(),
          exit_date: position.exitDate?.toISOString(),
          signal_id: position.signalId,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating position:', error);
        return null;
      }

      return this.mapPosition(data);
    } catch (error) {
      console.error('Error in createPosition:', error);
      return null;
    }
  }

  /**
   * Update position
   */
  static async updatePosition(positionId: string, updates: Partial<Position>): Promise<boolean> {
    try {
      const updateData: any = {};
      
      if (updates.amount !== undefined) updateData.amount = updates.amount;
      if (updates.currentPrice !== undefined) updateData.current_price = updates.currentPrice;
      if (updates.currentValue !== undefined) updateData.current_value = updates.currentValue;
      if (updates.pnl !== undefined) updateData.pnl = updates.pnl;
      if (updates.pnlPercentage !== undefined) updateData.pnl_percentage = updates.pnlPercentage;
      if (updates.multiplier !== undefined) updateData.multiplier = updates.multiplier;
      if (updates.status !== undefined) updateData.status = updates.status;
      if (updates.exitDate !== undefined) updateData.exit_date = updates.exitDate?.toISOString();

      const { error } = await supabase
        .from('positions')
        .update(updateData)
        .eq('id', positionId);

      if (error) {
        console.error('Error updating position:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updatePosition:', error);
      return false;
    }
  }

  /**
   * Record a trade
   */
  static async recordTrade(trade: Omit<Trade, 'id' | 'createdAt'>): Promise<Trade | null> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return null;
      }

      // Get user's portfolio
      const { data: portfolio, error: portfolioError } = await supabase
        .from('portfolios')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (portfolioError || !portfolio) {
        console.error('Error fetching portfolio:', portfolioError);
        return null;
      }

      const { data, error } = await supabase
        .from('trades')
        .insert({
          portfolio_id: portfolio.id,
          position_id: trade.positionId,
          type: trade.type,
          token_address: trade.tokenAddress,
          token_symbol: trade.tokenSymbol,
          amount: trade.amount,
          price: trade.price,
          value: trade.value,
          slippage: trade.slippage,
          fees: trade.fees,
          signature: trade.signature,
          status: trade.status,
          timestamp: trade.timestamp.toISOString(),
          signal_id: trade.signalId,
        })
        .select()
        .single();

      if (error) {
        console.error('Error recording trade:', error);
        return null;
      }

      return this.mapTrade(data);
    } catch (error) {
      console.error('Error in recordTrade:', error);
      return null;
    }
  }

  /**
   * Update portfolio totals
   */
  static async updatePortfolioTotals(totalValue: number, totalPnL: number, totalPnLPercentage: number): Promise<boolean> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return false;
      }

      const { error } = await supabase
        .from('portfolios')
        .update({
          total_value: totalValue,
          total_pnl: totalPnL,
          total_pnl_percentage: totalPnLPercentage,
        })
        .eq('user_id', user.id);

      if (error) {
        console.error('Error updating portfolio totals:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updatePortfolioTotals:', error);
      return false;
    }
  }

  /**
   * Map database position to Position type
   */
  private static mapPosition(data: any): Position {
    return {
      id: data.id,
      portfolioId: data.portfolio_id,
      tokenAddress: data.token_address,
      tokenSymbol: data.token_symbol,
      tokenName: data.token_name,
      amount: Number(data.amount),
      entryPrice: Number(data.entry_price),
      currentPrice: Number(data.current_price),
      entryValue: Number(data.entry_value),
      currentValue: Number(data.current_value),
      pnl: Number(data.pnl),
      pnlPercentage: Number(data.pnl_percentage),
      multiplier: Number(data.multiplier),
      status: data.status,
      entryDate: new Date(data.entry_date),
      exitDate: data.exit_date ? new Date(data.exit_date) : undefined,
      signalId: data.signal_id,
    };
  }

  /**
   * Map database trade to Trade type
   */
  private static mapTrade(data: any): Trade {
    return {
      id: data.id,
      portfolioId: data.portfolio_id,
      positionId: data.position_id,
      type: data.type,
      tokenAddress: data.token_address,
      tokenSymbol: data.token_symbol,
      amount: Number(data.amount),
      price: Number(data.price),
      value: Number(data.value),
      slippage: Number(data.slippage),
      fees: Number(data.fees),
      signature: data.signature,
      status: data.status,
      timestamp: new Date(data.timestamp),
      signalId: data.signal_id,
    };
  }
}
