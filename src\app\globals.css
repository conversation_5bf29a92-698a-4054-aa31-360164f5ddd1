@import "tailwindcss";

:root {
  /* Minimal Enterprise Color Palette - Monochromatic Focus */
  --background: #0a0a0a;
  --foreground: #f8fafc;

  /* Base Neutral Colors - High Contrast Monochromatic */
  --neutral-50: #f8fafc;
  --neutral-100: #f1f5f9;
  --neutral-200: #e2e8f0;
  --neutral-300: #cbd5e1;
  --neutral-400: #94a3b8;
  --neutral-500: #64748b;
  --neutral-600: #475569;
  --neutral-700: #334155;
  --neutral-800: #1e293b;
  --neutral-900: #0f172a;
  --neutral-950: #020617;

  /* Minimal Semantic Colors - Only Essential */
  --accent-success: #22c55e;     /* Only for profit/positive values */
  --accent-danger: #ef4444;      /* Only for loss/negative values */
  --accent-neutral: #64748b;     /* Neutral gray for most elements */
  --accent-subtle: #475569;      /* Subtle accent for borders/dividers */

  /* Degen Theme Colors - For gradients and accents */
  --accent-primary: #3b82f6;     /* Blue for primary actions */
  --accent-warning: #f59e0b;     /* Gold for warnings/premium */
  --accent-purple: #8b5cf6;      /* Purple for signals/special */

  /* Semantic Color Mapping - Minimal Usage */
  --color-profit: var(--accent-success);
  --color-loss: var(--accent-danger);
  --color-neutral: var(--accent-neutral);
  --color-text-primary: var(--neutral-50);
  --color-text-secondary: var(--neutral-300);
  --color-text-muted: var(--neutral-400);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;

  /* Minimal Neutral System */
  --color-neutral-50: #f8fafc;
  --color-neutral-100: #f1f5f9;
  --color-neutral-200: #e2e8f0;
  --color-neutral-300: #cbd5e1;
  --color-neutral-400: #94a3b8;
  --color-neutral-500: #64748b;
  --color-neutral-600: #475569;
  --color-neutral-700: #334155;
  --color-neutral-800: #1e293b;
  --color-neutral-900: #0f172a;
  --color-neutral-950: #020617;

  /* Minimal Semantic Colors - Only Essential */
  --color-success: #22c55e;
  --color-danger: #ef4444;
  --color-neutral-accent: #64748b;
  --color-subtle: #475569;

  /* Degen Theme Colors - For gradients and accents */
  --color-primary: #3b82f6;
  --color-warning: #f59e0b;
  --color-purple: #8b5cf6;

  /* Text Color System */
  --color-text-primary: var(--neutral-50);
  --color-text-secondary: var(--neutral-300);
  --color-text-muted: var(--neutral-400);

  /* Minimal Animations - Reduced */
  --animate-fade-in: fadeIn 0.3s ease-out;
  --animate-slide-up: slideUp 0.3s ease-out;
}

/* Professional body styling - Force dark theme */
body {
  background: linear-gradient(135deg, #020617 0%, #0f172a 100%) !important;
  color: #f8fafc !important;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
  min-height: 100vh;
}

/* Fallback using CSS variables */
body.dark {
  background: linear-gradient(135deg, var(--color-neutral-950, #020617) 0%, var(--color-neutral-900, #0f172a) 100%) !important;
  color: var(--color-foreground, #f8fafc) !important;
}

/* Minimal animations - Enterprise style */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

/* Data update animation - minimal */
@keyframes dataUpdate {
  0% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(100, 116, 139, 0.1);
  }
  100% {
    background-color: transparent;
  }
}

/* Utility classes for degen styling - REMOVED DUPLICATES */

/* Minimal gradient backgrounds - Monochromatic */
.bg-gradient-degen {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);
}

.bg-gradient-sophisticated {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);
}

.bg-gradient-profit {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(34, 197, 94, 0.05) 100%);
}

.bg-gradient-loss {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(239, 68, 68, 0.05) 100%);
}

.bg-gradient-neutral {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);
}

.bg-gradient-premium {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);
}

.bg-gradient-signal {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 100%);
}

/* Minimal text colors - High contrast with minimal color usage */
.text-degen-green { color: var(--color-success); }
.text-degen-red { color: var(--color-danger); }
.text-degen-blue { color: var(--color-primary); }
.text-degen-gold { color: var(--color-warning); }
.text-degen-purple { color: var(--color-purple); }
.text-degen-gray { color: var(--color-neutral-400); }
.text-degen-charcoal { color: var(--color-neutral-600); }

/* High contrast text utilities */
.text-primary { color: var(--color-text-primary); }
.text-secondary { color: var(--color-text-secondary); }
.text-muted { color: var(--color-text-muted); }
.text-subtle { color: var(--color-neutral-500); }

/* Minimal border colors - Neutral focus */
.border-degen-green { border-color: var(--color-success); }
.border-degen-red { border-color: var(--color-danger); }
.border-degen-blue { border-color: var(--color-neutral-600); }
.border-degen-gold { border-color: var(--color-neutral-600); }
.border-degen-purple { border-color: var(--color-neutral-600); }
.border-degen-gray { border-color: var(--color-neutral-700); }

/* Minimal border utilities */
.border-subtle { border-color: var(--color-neutral-800); }
.border-muted { border-color: var(--color-neutral-700); }
.border-default { border-color: var(--color-neutral-600); }

/* Minimal background colors - Monochromatic */
.bg-degen-green { background-color: rgba(34, 197, 94, 0.1); }
.bg-degen-red { background-color: rgba(239, 68, 68, 0.1); }
.bg-degen-blue { background-color: var(--color-neutral-800); }
.bg-degen-gold { background-color: var(--color-neutral-800); }
.bg-degen-purple { background-color: var(--color-neutral-800); }
.bg-degen-gray { background-color: var(--color-neutral-700); }
.bg-degen-charcoal { background-color: var(--color-neutral-800); }
.bg-degen-dark { background-color: var(--color-neutral-900); }

/* Minimal background utilities */
.bg-surface { background-color: var(--color-neutral-900); }
.bg-surface-elevated { background-color: var(--color-neutral-800); }
.bg-surface-overlay { background-color: var(--color-neutral-700); }

/* Minimal subtle effects - Enterprise style */
.glow-green {
  border: 1px solid rgba(34, 197, 94, 0.3);
  transition: border-color 0.2s ease;
}

.glow-green:hover {
  border-color: rgba(34, 197, 94, 0.5);
}

.glow-red {
  border: 1px solid rgba(239, 68, 68, 0.3);
  transition: border-color 0.2s ease;
}

.glow-red:hover {
  border-color: rgba(239, 68, 68, 0.5);
}

.glow-blue {
  border: 1px solid var(--color-neutral-600);
  transition: border-color 0.2s ease;
}

.glow-blue:hover {
  border-color: var(--color-neutral-500);
}

.glow-gold {
  border: 1px solid var(--color-neutral-600);
  transition: border-color 0.2s ease;
}

.glow-gold:hover {
  border-color: var(--color-neutral-500);
}

.glow-purple {
  border: 1px solid var(--color-neutral-600);
  transition: border-color 0.2s ease;
}

.glow-purple:hover {
  border-color: var(--color-neutral-500);
}

/* Minimal subtle gradient backgrounds */
.bg-gradient-profit-subtle {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(34, 197, 94, 0.02) 50%, rgba(34, 197, 94, 0.05) 100%);
}

.bg-gradient-loss-subtle {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.05) 0%, rgba(239, 68, 68, 0.02) 50%, rgba(239, 68, 68, 0.05) 100%);
}

.bg-gradient-neutral-subtle {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 50%, var(--color-neutral-800) 100%);
}

.bg-gradient-premium-subtle {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 50%, var(--color-neutral-800) 100%);
}

.bg-gradient-signal-subtle {
  background: linear-gradient(135deg, var(--color-neutral-800) 0%, var(--color-neutral-900) 50%, var(--color-neutral-800) 100%);
}

/* Professional subtle border gradients */
.border-gradient-green {
  border: 1px solid;
  border-image: linear-gradient(135deg, var(--accent-success), rgba(16, 185, 129, 0.3)) 1;
}

.border-gradient-red {
  border: 1px solid;
  border-image: linear-gradient(135deg, var(--accent-danger), rgba(239, 68, 68, 0.3)) 1;
}

.border-gradient-blue {
  border: 1px solid;
  border-image: linear-gradient(135deg, var(--accent-primary), rgba(59, 130, 246, 0.3)) 1;
}

.border-gradient-gold {
  border: 1px solid;
  border-image: linear-gradient(135deg, var(--accent-warning), rgba(245, 158, 11, 0.3)) 1;
}

.border-gradient-purple {
  border: 1px solid;
  border-image: linear-gradient(135deg, var(--accent-purple), rgba(139, 92, 246, 0.3)) 1;
}

/* Smooth transitions */
.transition-colors-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift {
  transition: transform 0.2s ease;
}

.hover-lift:hover {
  transform: translateY(-2px);
}

/* Minimal Interactive Effects - Enterprise style */
.interactive-card {
  transition: all 0.2s ease;
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.pulse-data {
  animation: dataUpdate 0.5s ease-out;
}

.slide-up-enter {
  animation: slideUp 0.3s ease-out;
}

.fade-in-enter {
  animation: fadeIn 0.3s ease-out;
}

/* Minimal Button Enhancement */
.btn-enhanced {
  transition: all 0.2s ease;
}

.btn-enhanced:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Minimal loading states */
.loading-pulse {
  opacity: 0.6;
}

/* Minimal price change indicators */
.price-up {
  color: var(--color-success);
}

.price-down {
  color: var(--color-danger);
}

.price-neutral {
  color: var(--color-text-secondary);
}

/* Shimmer Text Animations */
@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes shimmer-overlay {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.animate-shimmer {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-shimmer-slow {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite;
}

.animate-shimmer-fast {
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  background-size: 200% 100%;
  animation: shimmer 1s infinite;
}

.animate-shimmer-overlay {
  animation: shimmer-overlay 2s infinite;
}
