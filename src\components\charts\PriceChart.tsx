'use client';

import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { formatCurrency, formatRelativeTime, getChangeColor } from '@/lib/utils';
import { cn } from '@/lib/utils';
import type { TokenPrice } from '@/types';

interface PriceChartProps {
  tokenAddress: string;
  tokenSymbol?: string;
  priceHistory: TokenPrice[];
  currentPrice?: number;
  priceChange24h?: number;
  className?: string;
  height?: number;
}

export function PriceChart({
  tokenAddress,
  tokenSymbol,
  priceHistory,
  currentPrice,
  priceChange24h,
  className,
  height = 300,
}: PriceChartProps) {
  // Prepare chart data
  const chartData = priceHistory.map((price, index) => ({
    timestamp: price.timestamp.getTime(),
    price: price.price,
    formattedTime: formatRelativeTime(price.timestamp),
    index,
  }));

  // Determine if price is going up or down
  const isPositive = (priceChange24h || 0) >= 0;
  const lineColor = isPositive ? '#22C55E' : '#EF4444'; // green or red

  // Custom tooltip
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-3 shadow-lg">
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {new Date(label).toLocaleString()}
          </p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {formatCurrency(data.price, 'USD', data.price < 0.01 ? 6 : 4)}
          </p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">
              {tokenSymbol || 'Token'} Price Chart
            </CardTitle>
            <p className="text-sm text-gray-600 dark:text-gray-400 font-mono">
              {tokenAddress.slice(0, 8)}...{tokenAddress.slice(-8)}
            </p>
          </div>
          
          <div className="text-right">
            {currentPrice && (
              <div className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(currentPrice, 'USD', currentPrice < 0.01 ? 6 : 4)}
              </div>
            )}
            {priceChange24h !== undefined && (
              <Badge 
                variant={isPositive ? 'success' : 'error'} 
                size="sm"
                className="mt-1"
              >
                {isPositive ? '+' : ''}{priceChange24h.toFixed(2)}%
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {chartData.length === 0 ? (
          <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <svg className="w-12 h-12 mx-auto mb-2 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              <p>No price data available</p>
            </div>
          </div>
        ) : chartData.length === 1 ? (
          <div className="flex items-center justify-center h-64 text-gray-500 dark:text-gray-400">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                {formatCurrency(chartData[0].price, 'USD', chartData[0].price < 0.01 ? 6 : 4)}
              </div>
              <p>Single price point - chart will appear with more data</p>
            </div>
          </div>
        ) : (
          <div style={{ height }}>
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" className="opacity-30" />
                <XAxis 
                  dataKey="timestamp"
                  type="number"
                  scale="time"
                  domain={['dataMin', 'dataMax']}
                  tickFormatter={(timestamp) => {
                    const date = new Date(timestamp);
                    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                  }}
                  className="text-xs"
                />
                <YAxis 
                  domain={['dataMin - dataMin * 0.01', 'dataMax + dataMax * 0.01']}
                  tickFormatter={(value) => formatCurrency(value, 'USD', value < 0.01 ? 6 : 2)}
                  className="text-xs"
                />
                <Tooltip content={<CustomTooltip />} />
                <Line
                  type="monotone"
                  dataKey="price"
                  stroke={lineColor}
                  strokeWidth={2}
                  dot={false}
                  activeDot={{ r: 4, fill: lineColor }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Chart Info */}
        {chartData.length > 1 && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <p className="text-gray-600 dark:text-gray-400">Data Points</p>
                <p className="font-medium">{chartData.length}</p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Time Range</p>
                <p className="font-medium">
                  {formatRelativeTime(priceHistory[0]?.timestamp)}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Highest</p>
                <p className="font-medium">
                  {formatCurrency(Math.max(...chartData.map(d => d.price)), 'USD', 4)}
                </p>
              </div>
              <div>
                <p className="text-gray-600 dark:text-gray-400">Lowest</p>
                <p className="font-medium">
                  {formatCurrency(Math.min(...chartData.map(d => d.price)), 'USD', 4)}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Skeleton loader for price chart
export function PriceChartSkeleton({ className, height = 300 }: { className?: string; height?: number }) {
  return (
    <Card className={cn('w-full animate-pulse', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div>
            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
          </div>
          <div className="text-right">
            <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-20 mb-2"></div>
            <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div style={{ height }} className="bg-gray-200 dark:bg-gray-700 rounded"></div>
      </CardContent>
    </Card>
  );
}
