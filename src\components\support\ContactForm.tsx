'use client';

import React, { useState } from 'react';
import { Modal } from '@/components/ui/Modal';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { PulseDot } from '@/components/ui/LiveIndicator';
import { DEGEN_EMOJIS } from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import { SupportService } from '@/lib/supabase/services/supportService';
import { useSupabaseAuth } from '@/hooks/useSupabase';
import { cn } from '@/lib/utils';

interface ContactFormProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FormData {
  email: string;
  category: 'technical' | 'trading' | 'account' | 'feature_request' | 'general';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
}

interface FormErrors {
  email?: string;
  title?: string;
  description?: string;
}

export function ContactForm({ isOpen, onClose }: ContactFormProps) {
  const { user } = useSupabaseAuth();
  const [formData, setFormData] = useState<FormData>({
    email: '',
    category: 'general',
    priority: 'medium',
    title: '',
    description: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const categories = [
    { value: 'technical', label: 'Technical Issues', icon: 'settings', color: 'text-degen-red' },
    { value: 'trading', label: 'Trading Problems', icon: 'trading', color: 'text-degen-green' },
    { value: 'account', label: 'Account Issues', icon: 'wallet', color: 'text-degen-blue' },
    { value: 'feature_request', label: 'Feature Request', icon: 'rocket', color: 'text-degen-purple' },
    { value: 'general', label: 'General Help', icon: 'target', color: 'text-degen-gold' },
  ];

  const priorities = [
    { value: 'low', label: 'Low', color: 'bg-degen-gray', description: 'General questions, minor issues' },
    { value: 'medium', label: 'Medium', color: 'bg-degen-blue', description: 'Standard support requests' },
    { value: 'high', label: 'High', color: 'bg-degen-gold', description: 'Urgent issues affecting trading' },
    { value: 'critical', label: 'Critical', color: 'bg-degen-red', description: 'System down, funds at risk' },
  ];

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    } else if (formData.title.length < 5) {
      newErrors.title = 'Title must be at least 5 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 20) {
      newErrors.description = 'Please provide more details (at least 20 characters)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const { data, error } = await SupportService.createTicket({
        user_id: user?.id || 'anonymous',
        email: formData.email,
        category: formData.category,
        priority: formData.priority,
        title: formData.title,
        description: formData.description,
      });

      if (error) {
        setSubmitError(error);
      } else {
        setSubmitSuccess(true);
        // Reset form after successful submission
        setTimeout(() => {
          setFormData({
            email: '',
            category: 'general',
            priority: 'medium',
            title: '',
            description: '',
          });
          setSubmitSuccess(false);
          onClose();
        }, 2000);
      }
    } catch (error) {
      setSubmitError('Failed to submit ticket. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field as keyof FormErrors]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  if (submitSuccess) {
    return (
      <Modal isOpen={isOpen} onClose={onClose} title="Ticket Submitted">
        <div className="text-center space-y-4 p-6">
          <div className="text-6xl">{DEGEN_EMOJIS.rocket}</div>
          <h3 className="text-xl font-bold text-degen-green">
            Support Ticket Created Successfully!
          </h3>
          <p className="text-degen-gray">
            We've received your request and will get back to you soon. 
            Check your email for updates on your ticket.
          </p>
          <div className="flex items-center justify-center gap-2">
            <PulseDot color="green" size="sm" />
            <span className="text-sm text-degen-green font-medium">Ticket submitted</span>
          </div>
        </div>
      </Modal>
    );
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} title="Contact Support" size="lg">
      <form onSubmit={handleSubmit} className="space-y-6 p-6">
        {/* Header */}
        <div className="text-center">
          <h3 className="text-lg font-bold text-primary mb-2">
            Get Help from the Signal V1 Team {DEGEN_EMOJIS.target}
          </h3>
          <p className="text-sm text-degen-gray">
            Describe your issue and we'll help you get back to making gains
          </p>
        </div>

        {/* Email */}
        <div>
          <Input
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            placeholder="<EMAIL>"
            error={errors.email}
            className="bg-gradient-neutral-subtle border-gradient-blue text-white placeholder:text-degen-gray"
          />
        </div>

        {/* Category Selection */}
        <div>
          <label className="block text-sm font-medium text-primary mb-3">
            Issue Category
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
            {categories.map((category) => (
              <button
                key={category.value}
                type="button"
                onClick={() => handleInputChange('category', category.value)}
                className={cn(
                  'flex items-center gap-3 p-3 rounded-lg border transition-all duration-200',
                  formData.category === category.value
                    ? 'border-degen-purple bg-gradient-sophisticated text-white'
                    : 'border-degen-gray/30 bg-gradient-neutral-subtle text-degen-gray hover:border-degen-purple/50'
                )}
              >
                <Icon 
                  name={category.icon} 
                  size="sm" 
                  color={formData.category === category.value ? 'white' : 'gray'} 
                />
                <span className="text-sm font-medium">{category.label}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Priority Selection */}
        <div>
          <label className="block text-sm font-medium text-primary mb-3">
            Priority Level
          </label>
          <div className="grid grid-cols-2 gap-2">
            {priorities.map((priority) => (
              <button
                key={priority.value}
                type="button"
                onClick={() => handleInputChange('priority', priority.value)}
                className={cn(
                  'flex flex-col items-start gap-1 p-3 rounded-lg border transition-all duration-200',
                  formData.priority === priority.value
                    ? 'border-degen-purple bg-gradient-sophisticated text-white'
                    : 'border-degen-gray/30 bg-gradient-neutral-subtle text-degen-gray hover:border-degen-purple/50'
                )}
              >
                <div className="flex items-center gap-2">
                  <div className={cn('w-2 h-2 rounded-full', priority.color)}></div>
                  <span className="text-sm font-medium">{priority.label}</span>
                </div>
                <span className="text-xs opacity-75">{priority.description}</span>
              </button>
            ))}
          </div>
        </div>

        {/* Title */}
        <div>
          <Input
            label="Issue Title"
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            placeholder="Brief description of your issue"
            error={errors.title}
            className="bg-gradient-neutral-subtle border-gradient-blue text-white placeholder:text-degen-gray"
          />
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-primary mb-2">
            Detailed Description
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder="Please provide as much detail as possible about your issue, including steps to reproduce, error messages, etc."
            rows={4}
            className={cn(
              'w-full px-3 py-2 rounded-lg border bg-gradient-neutral-subtle border-gradient-blue text-white placeholder:text-degen-gray',
              'focus:outline-none focus:ring-2 focus:ring-degen-purple focus:border-transparent',
              errors.description && 'border-red-500 focus:ring-red-500'
            )}
          />
          {errors.description && (
            <p className="mt-1 text-sm text-red-500">{errors.description}</p>
          )}
          <p className="mt-1 text-xs text-degen-gray">
            {formData.description.length}/500 characters
          </p>
        </div>

        {/* Submit Error */}
        {submitError && (
          <div className="p-3 rounded-lg bg-red-500/20 border border-red-500/30">
            <p className="text-sm text-red-400">{submitError}</p>
          </div>
        )}

        {/* Actions */}
        <div className="flex gap-3 pt-4 border-t border-degen-purple/30">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
            className="flex-1"
          >
            Cancel
          </Button>
          <Button
            type="submit"
            variant="ape"
            loading={isSubmitting}
            disabled={isSubmitting}
            className="flex-1 font-bold"
          >
            {isSubmitting ? (
              <>
                <PulseDot color="green" size="sm" className="mr-2" />
                Submitting...
              </>
            ) : (
              <>
                <Icon name="target" size="sm" color="white" className="mr-1" />
                Submit Ticket
              </>
            )}
          </Button>
        </div>
      </form>
    </Modal>
  );
}
