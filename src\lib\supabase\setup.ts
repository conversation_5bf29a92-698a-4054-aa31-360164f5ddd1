// Supabase Database Setup and Verification
// Ensures required tables exist and creates them if missing

import { supabase } from './client';

interface TableSchema {
  name: string;
  createSQL: string;
  description: string;
}

const REQUIRED_TABLES: TableSchema[] = [
  {
    name: 'telegram_channels',
    description: 'Stores Telegram channel information for signal monitoring',
    createSQL: `
      CREATE TABLE IF NOT EXISTS telegram_channels (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        name TEXT NOT NULL,
        username TEXT UNIQUE NOT NULL,
        description TEXT,
        member_count INTEGER DEFAULT 0,
        active BOOLEAN DEFAULT true,
        signal_count INTEGER DEFAULT 0,
        last_signal TIMESTAMP WITH TIME ZONE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_telegram_channels_active ON telegram_channels(active);
      CREATE INDEX IF NOT EXISTS idx_telegram_channels_username ON telegram_channels(username);
      CREATE INDEX IF NOT EXISTS idx_telegram_channels_signal_count ON telegram_channels(signal_count DESC);
    `
  },
  {
    name: 'signals',
    description: 'Stores parsed trading signals from Telegram channels',
    createSQL: `
      CREATE TABLE IF NOT EXISTS signals (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        channel_id UUID REFERENCES telegram_channels(id),
        token_symbol TEXT,
        token_address TEXT,
        signal_type TEXT CHECK (signal_type IN ('BUY', 'SELL', 'HOLD')),
        price DECIMAL,
        market_cap DECIMAL,
        confidence_score DECIMAL CHECK (confidence_score >= 0 AND confidence_score <= 1),
        raw_message TEXT,
        parsed_data JSONB,
        valid BOOLEAN DEFAULT true,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Create indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_signals_channel_id ON signals(channel_id);
      CREATE INDEX IF NOT EXISTS idx_signals_token_address ON signals(token_address);
      CREATE INDEX IF NOT EXISTS idx_signals_created_at ON signals(created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_signals_valid ON signals(valid);
    `
  },
  {
    name: 'user_settings',
    description: 'Stores user configuration and preferences',
    createSQL: `
      CREATE TABLE IF NOT EXISTS user_settings (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id TEXT UNIQUE NOT NULL,
        wallet_address TEXT,
        auto_trading_enabled BOOLEAN DEFAULT false,
        default_trade_amount DECIMAL DEFAULT 0.1,
        max_position_percentage DECIMAL DEFAULT 5.0,
        stop_loss_percentage DECIMAL DEFAULT 30.0,
        take_profit_percentage DECIMAL DEFAULT 100.0,
        max_active_positions INTEGER DEFAULT 10,
        telegram_channels TEXT[],
        settings_data JSONB,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
      CREATE INDEX IF NOT EXISTS idx_user_settings_wallet_address ON user_settings(wallet_address);
    `
  },
  {
    name: 'trading_history',
    description: 'Stores trading transaction history',
    createSQL: `
      CREATE TABLE IF NOT EXISTS trading_history (
        id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
        user_id TEXT NOT NULL,
        wallet_address TEXT NOT NULL,
        signal_id UUID REFERENCES signals(id),
        transaction_type TEXT CHECK (transaction_type IN ('BUY', 'SELL')),
        token_symbol TEXT,
        token_address TEXT,
        amount_sol DECIMAL,
        amount_tokens DECIMAL,
        price_per_token DECIMAL,
        transaction_hash TEXT,
        status TEXT CHECK (status IN ('PENDING', 'SUCCESS', 'FAILED')),
        error_message TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      -- Create indexes
      CREATE INDEX IF NOT EXISTS idx_trading_history_user_id ON trading_history(user_id);
      CREATE INDEX IF NOT EXISTS idx_trading_history_wallet_address ON trading_history(wallet_address);
      CREATE INDEX IF NOT EXISTS idx_trading_history_created_at ON trading_history(created_at DESC);
      CREATE INDEX IF NOT EXISTS idx_trading_history_status ON trading_history(status);
    `
  }
];

export class DatabaseSetup {
  /**
   * Check if all required tables exist
   */
  static async checkTablesExist(): Promise<{ [tableName: string]: boolean }> {
    const results: { [tableName: string]: boolean } = {};
    
    for (const table of REQUIRED_TABLES) {
      try {
        const { data, error } = await supabase
          .from(table.name)
          .select('*')
          .limit(1);
        
        results[table.name] = !error;
        
        if (error) {
          console.log(`❌ Table '${table.name}' does not exist or is not accessible:`, error.message);
        } else {
          console.log(`✅ Table '${table.name}' exists and is accessible`);
        }
      } catch (error) {
        console.error(`❌ Error checking table '${table.name}':`, error);
        results[table.name] = false;
      }
    }
    
    return results;
  }

  /**
   * Create missing tables
   * Note: This requires elevated permissions and may not work with anon key
   */
  static async createMissingTables(): Promise<{ success: boolean; errors: string[] }> {
    const errors: string[] = [];
    let success = true;

    console.log('🔧 Attempting to create missing database tables...');
    console.warn('⚠️ Note: Table creation requires elevated permissions and may fail with anon key');

    for (const table of REQUIRED_TABLES) {
      try {
        console.log(`🔨 Creating table: ${table.name}`);
        
        // Note: This will likely fail with anon key, but we'll try anyway
        const { error } = await supabase.rpc('exec_sql', { 
          sql: table.createSQL 
        });

        if (error) {
          const errorMsg = `Failed to create table '${table.name}': ${error.message}`;
          console.error(`❌ ${errorMsg}`);
          errors.push(errorMsg);
          success = false;
        } else {
          console.log(`✅ Successfully created table: ${table.name}`);
        }
      } catch (error) {
        const errorMsg = `Exception creating table '${table.name}': ${error instanceof Error ? error.message : 'Unknown error'}`;
        console.error(`❌ ${errorMsg}`);
        errors.push(errorMsg);
        success = false;
      }
    }

    return { success, errors };
  }

  /**
   * Get SQL scripts for manual database setup
   */
  static getDatabaseSetupSQL(): string {
    const header = `-- Signal V1 Database Setup
-- Run these SQL commands in your Supabase SQL editor
-- Generated on: ${new Date().toISOString()}

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

`;

    const tableSQL = REQUIRED_TABLES
      .map(table => `-- ${table.description}\n${table.createSQL}`)
      .join('\n\n');

    const footer = `
-- Enable Row Level Security (RLS) for security
ALTER TABLE telegram_channels ENABLE ROW LEVEL SECURITY;
ALTER TABLE signals ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE trading_history ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies (adjust as needed)
-- Allow read access to telegram_channels and signals for all authenticated users
CREATE POLICY "Allow read access to telegram_channels" ON telegram_channels FOR SELECT USING (true);
CREATE POLICY "Allow read access to signals" ON signals FOR SELECT USING (true);

-- Allow users to manage their own settings and trading history
CREATE POLICY "Users can manage their own settings" ON user_settings FOR ALL USING (auth.uid()::text = user_id);
CREATE POLICY "Users can manage their own trading history" ON trading_history FOR ALL USING (auth.uid()::text = user_id);
`;

    return header + tableSQL + footer;
  }

  /**
   * Verify database setup and provide guidance
   */
  static async verifySetup(): Promise<{
    tablesExist: { [tableName: string]: boolean };
    allTablesExist: boolean;
    setupSQL: string;
    recommendations: string[];
  }> {
    console.log('🔍 Verifying database setup...');
    
    const tablesExist = await this.checkTablesExist();
    const allTablesExist = Object.values(tablesExist).every(exists => exists);
    const setupSQL = this.getDatabaseSetupSQL();
    
    const recommendations: string[] = [];
    
    if (!allTablesExist) {
      recommendations.push('❌ Some required tables are missing from your Supabase database');
      recommendations.push('📋 Copy the generated SQL script and run it in your Supabase SQL editor');
      recommendations.push('🔗 Access SQL editor at: https://supabase.com/dashboard/project/[your-project]/sql');
      recommendations.push('⚠️ Make sure to adjust RLS policies according to your security requirements');
    } else {
      recommendations.push('✅ All required tables exist and are accessible');
      recommendations.push('🎉 Your database setup is complete!');
    }

    return {
      tablesExist,
      allTablesExist,
      setupSQL,
      recommendations
    };
  }

  /**
   * Insert demo data for development
   */
  static async insertDemoData(): Promise<boolean> {
    try {
      console.log('📝 Inserting demo data...');
      
      // Insert demo channels
      const { error: channelsError } = await supabase
        .from('telegram_channels')
        .upsert([
          {
            name: 'Crypto Signals Pro',
            username: 'cryptosignalspro',
            description: 'Premium crypto trading signals',
            member_count: 15420,
            active: true,
            signal_count: 127
          },
          {
            name: 'Alpha Hunters',
            username: 'alphahunters',
            description: 'Early alpha calls and gems',
            member_count: 8930,
            active: true,
            signal_count: 89
          }
        ], { onConflict: 'username' });

      if (channelsError) {
        console.error('❌ Error inserting demo channels:', channelsError);
        return false;
      }

      console.log('✅ Demo data inserted successfully');
      return true;
    } catch (error) {
      console.error('❌ Error inserting demo data:', error);
      return false;
    }
  }
}

// Export setup functions for easy access
export const {
  checkTablesExist,
  createMissingTables,
  getDatabaseSetupSQL,
  verifySetup,
  insertDemoData
} = DatabaseSetup;
