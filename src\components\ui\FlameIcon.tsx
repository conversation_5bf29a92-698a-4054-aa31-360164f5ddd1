'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface FlameIconProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
  className?: string;
}

export function FlameIcon({ 
  size = 'md', 
  color = 'white',
  className 
}: FlameIconProps) {
  const sizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4', 
    lg: 'w-5 h-5'
  };

  return (
    <svg
      className={cn(sizes[size], className)}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12 2C12 2 8 6 8 10C8 12.2091 9.79086 14 12 14C14.2091 14 16 12.2091 16 10C16 6 12 2 12 2Z"
        fill={color}
        fillOpacity="0.9"
      />
      <path
        d="M12 14C12 14 9 16 9 18.5C9 19.8807 10.1193 21 11.5 21C12.8807 21 14 19.8807 14 18.5C14 16 12 14 12 14Z"
        fill={color}
        fillOpacity="0.7"
      />
      <path
        d="M12 8C12 8 10 10 10 11.5C10 12.3284 10.6716 13 11.5 13C12.3284 13 13 12.3284 13 11.5C13 10 12 8 12 8Z"
        fill={color}
        fillOpacity="0.5"
      />
    </svg>
  );
}
