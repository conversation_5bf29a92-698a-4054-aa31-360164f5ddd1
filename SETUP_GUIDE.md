# 🚀 Signal V1 - Complete Setup & Testing Guide

## 📋 **IMMEDIATE SETUP REQUIRED**

### **Step 1: Supabase Database Setup**
1. **Create Supabase Project**
   - Go to [supabase.com](https://supabase.com)
   - Create new project
   - Note your Project URL and API keys

2. **Run Database Schema**
   ```sql
   -- Copy and run the contents of supabase/schema.sql in your Supabase SQL editor
   ```

3. **Update Environment Variables**
   Edit `.env.local` and replace:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here
   ```

### **Step 2: Test Application Components**

#### **✅ Wallet Connection Test**
1. Open http://localhost:3000
2. Click "Connect Wallet" button
3. Test with <PERSON>, <PERSON>flar<PERSON>, or other supported wallets
4. Verify wallet address displays correctly

#### **✅ Navigation Test**
- **Portfolio**: http://localhost:3000/portfolio
- **Signals**: http://localhost:3000/signals  
- **Trading**: http://localhost:3000/trading
- **Analytics**: http://localhost:3000/analytics
- **Settings**: http://localhost:3000/settings

#### **✅ Core Functionality Test**
1. **Mock Data Display**: Verify portfolio shows sample positions
2. **Charts Rendering**: Check price charts load without errors
3. **Responsive Design**: Test on mobile viewport (F12 → mobile view)
4. **PWA Features**: Check service worker registration in DevTools

---

## 🚀 **DEPLOYMENT CHECKLIST**

### **Production Environment Variables**
```env
# Production Supabase
NEXT_PUBLIC_SUPABASE_URL=https://your-prod-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_prod_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_prod_service_role_key

# Production Solana (Mainnet)
NEXT_PUBLIC_SOLANA_NETWORK=mainnet-beta
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.mainnet-beta.solana.com

# Production App URL
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Telegram Bot (Optional)
TELEGRAM_BOT_TOKEN=your_bot_token
TELEGRAM_WEBHOOK_URL=https://your-domain.com/api/telegram/webhook

# Trading Limits (Start Conservative)
DEFAULT_TRADE_AMOUNT_SOL=0.01
MAX_POSITION_PERCENTAGE=2
STOP_LOSS_PERCENTAGE=20
MAX_ACTIVE_POSITIONS=5
```

### **Deployment Platforms**

#### **🔥 Vercel (Recommended)**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy
vercel --prod

# Add environment variables in Vercel dashboard
```

#### **🐳 Docker**
```bash
# Build image
docker build -t signal-v1 .

# Run container
docker run -p 3000:3000 signal-v1
```

---

## 🧪 **SAFE TESTING PROCEDURES**

### **Phase 1: Development Testing (Current)**
- ✅ Use Solana Devnet for testing
- ✅ Test with small amounts (0.001-0.01 SOL)
- ✅ Verify all UI components work
- ✅ Test wallet connections

### **Phase 2: Staging Testing**
```env
# Use Solana Devnet
NEXT_PUBLIC_SOLANA_NETWORK=devnet
NEXT_PUBLIC_SOLANA_RPC_URL=https://api.devnet.solana.com
```

**Testing Checklist:**
- [ ] Wallet connection and disconnection
- [ ] Portfolio data loading
- [ ] Signal parsing (mock signals)
- [ ] Trading interface (without real trades)
- [ ] Price monitoring
- [ ] Settings persistence

### **Phase 3: Mainnet Testing (EXTREME CAUTION)**
```env
# Conservative settings for initial mainnet testing
DEFAULT_TRADE_AMOUNT_SOL=0.001  # Very small amounts
MAX_POSITION_PERCENTAGE=1       # 1% max position size
STOP_LOSS_PERCENTAGE=10         # Tight stop losses
MAX_ACTIVE_POSITIONS=3          # Limited positions
```

**⚠️ CRITICAL SAFETY MEASURES:**
1. **Start with tiny amounts** (0.001-0.01 SOL)
2. **Test one feature at a time**
3. **Monitor all transactions closely**
4. **Have manual override capabilities**
5. **Keep detailed logs**

---

## 🔧 **CONFIGURATION PRIORITIES**

### **High Priority (Required for Basic Function)**
1. ✅ Supabase database setup
2. ✅ Environment variables configuration
3. ✅ Wallet adapter testing
4. ✅ Basic UI functionality

### **Medium Priority (Required for Trading)**
1. 🔄 Telegram bot setup (optional for manual trading)
2. 🔄 Jupiter DEX integration testing
3. 🔄 Price monitoring configuration
4. 🔄 Risk management settings

### **Low Priority (Enhancement Features)**
1. 📱 PWA icon generation
2. 📊 Advanced analytics
3. 🔔 Push notifications
4. 📈 Advanced charting

---

## 🚨 **KNOWN ISSUES & FIXES**

### **Current Issues:**
1. **Supabase Connection**: Placeholder values need replacement
2. **PWA Icons**: Missing icon files (non-critical)
3. **Metadata Warnings**: Next.js viewport warnings (cosmetic)

### **Quick Fixes:**
```bash
# Fix PWA icons (optional)
mkdir -p public/icons
# Add your icon files or use placeholder

# Fix metadata warnings
# Update src/app/layout.tsx to use viewport export
```

---

## 📞 **SUPPORT & TROUBLESHOOTING**

### **Common Issues:**
1. **"supabaseUrl is required"** → Update .env.local with real Supabase values
2. **Wallet not connecting** → Check browser wallet extensions
3. **Charts not loading** → Verify Recharts dependencies
4. **API errors** → Check network connectivity and API keys

### **Debug Commands:**
```bash
# Check environment variables
npm run build

# Run tests
npm run test

# Type checking
npm run type-check

# Production build test
npm run build && npm run start
```

---

## 🎯 **SUCCESS METRICS**

### **Development Phase:**
- [ ] All pages load without errors
- [ ] Wallet connects successfully
- [ ] Mock data displays correctly
- [ ] Responsive design works

### **Testing Phase:**
- [ ] Real wallet transactions work
- [ ] Price data updates correctly
- [ ] Trading logic executes properly
- [ ] Risk management functions

### **Production Phase:**
- [ ] Live trading with small amounts
- [ ] Signal processing works
- [ ] Portfolio tracking accurate
- [ ] No critical errors in logs

---

**🚀 Ready to proceed with Supabase setup and wallet testing!**
