import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  /* config options here */

  // Temporarily bypass ESLint for production builds
  eslint: {
    ignoreDuringBuilds: true,
  },

  // Enhanced configuration for Solana wallet adapters and HMR stability
  experimental: {
    // Optimize for better HMR stability with dynamic imports
    optimizePackageImports: [
      '@solana/wallet-adapter-phantom',
      '@solana/wallet-adapter-solflare',
      '@solana/wallet-adapter-backpack',
      '@solana/wallet-adapter-glow',
      '@solana/wallet-adapter-base',
      '@solana/wallet-adapter-react',
    ],
    // Remove esmExternals as it's not recommended with Turbopack
  },

  // Note: Turbopack configuration moved to experimental section for Next.js 15
  // turbo configuration is handled automatically by Next.js 15

  // Webpack configuration for better module handling
  webpack: (config, { dev, isServer }) => {
    // Enhanced configuration for wallet adapters in development
    if (dev && !isServer) {
      // Improve HMR stability for wallet adapter modules
      config.optimization = {
        ...config.optimization,
        moduleIds: 'named', // Use named module IDs for better HMR stability
        splitChunks: {
          ...config.optimization.splitChunks,
          chunks: 'all',
          cacheGroups: {
            ...config.optimization.splitChunks?.cacheGroups,
            // Create separate chunks for large dependencies
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            // Separate chunk for Solana wallet adapters
            walletAdapters: {
              test: /[\\/]node_modules[\\/]@solana[\\/]wallet-adapter/,
              name: 'wallet-adapters',
              chunks: 'all',
              priority: 20,
            },
          },
        },
      };

      // Add specific handling for wallet adapter modules
      config.module.rules.push({
        test: /node_modules\/@solana\/wallet-adapter-/,
        sideEffects: false, // Mark wallet adapters as side-effect free for better tree shaking
      });

      // Add error boundary for chunk loading failures
      config.output = {
        ...config.output,
        chunkLoadingGlobal: 'webpackChunkSignalV1',
        crossOriginLoading: false,
      };
    }

    return config;
  },
};

export default nextConfig;
