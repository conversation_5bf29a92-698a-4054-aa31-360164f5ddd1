'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { SignalService } from '@/lib/supabase/services/signalService';
import { DatabaseSetup } from '@/lib/supabase/setup';
import Link from 'next/link';

export default function TestFixesPage() {
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testSignalServiceFix = async () => {
    addTestResult('🧪 Testing SignalService.getAllChannels fix...');
    
    try {
      const channels = await SignalService.getAllChannels();
      addTestResult(`✅ SignalService fix verified - got ${channels.length} channels without console errors`);
      
      if (channels.length > 0) {
        addTestResult(`📋 Sample channel: ${channels[0].name} (@${channels[0].username})`);
      } else {
        addTestResult('ℹ️ No channels returned - this is expected if database tables don\'t exist yet');
      }
    } catch (error) {
      addTestResult(`❌ SignalService test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('SignalService test error:', error);
    }
  };

  const testDatabaseSetup = async () => {
    addTestResult('🔍 Testing database setup verification...');
    
    try {
      const status = await DatabaseSetup.verifySetup();
      addTestResult(`📊 Database verification completed`);
      addTestResult(`✅ Tables found: ${Object.values(status.tablesExist).filter(Boolean).length}/${Object.keys(status.tablesExist).length}`);
      
      if (status.allTablesExist) {
        addTestResult('🎉 All required tables exist!');
      } else {
        addTestResult('❌ Some tables are missing - check /setup-database for instructions');
      }
    } catch (error) {
      addTestResult(`❌ Database verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    addTestResult('🚀 Starting comprehensive fix verification...');
    
    await testSignalServiceFix();
    await testDatabaseSetup();
    
    addTestResult('🏁 All automated tests completed');
    addTestResult('📋 Manual verification required for UI fixes - see instructions below');
    
    setIsLoading(false);
  };

  useEffect(() => {
    runAllTests();
  }, []);

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Fix Verification Tests
          </h1>
          <p className="text-gray-400">
            Verify that all reported issues have been resolved
          </p>
        </div>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Automated Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-800 p-4 rounded max-h-64 overflow-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-400">Running tests...</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
            <div className="mt-4 flex gap-2">
              <Button onClick={runAllTests} disabled={isLoading} variant="outline">
                {isLoading ? 'Running Tests...' : 'Re-run Tests'}
              </Button>
              <Button onClick={() => setTestResults([])} variant="outline">
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Fix Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Issues Fixed Summary</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-4 bg-green-900/50 border border-green-500/50 rounded-lg">
                <h3 className="font-bold text-green-400 mb-2">✅ 1. SignalService Console Errors</h3>
                <p className="text-sm text-gray-300 mb-2">
                  <strong>Fixed:</strong> Enhanced error handling in SignalService.getAllChannels method
                </p>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Added comprehensive error logging with detailed error information</li>
                  <li>• Created database setup utility to diagnose missing tables</li>
                  <li>• Improved Settings page error handling with user-friendly messages</li>
                  <li>• Added database verification and setup tools</li>
                </ul>
              </div>

              <div className="p-4 bg-green-900/50 border border-green-500/50 rounded-lg">
                <h3 className="font-bold text-green-400 mb-2">✅ 2. Active Diamond Hands Display Labels</h3>
                <p className="text-sm text-gray-300 mb-2">
                  <strong>Fixed:</strong> Updated portfolio display labels for better clarity
                </p>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Changed "BAG SIZE" to "Initial Investment" showing entry value</li>
                  <li>• Changed "ENTRY" to "Initial MC" showing initial market cap</li>
                  <li>• Changed "CURRENT" to "Current MC" showing current market cap</li>
                  <li>• Improved market cap calculations and formatting</li>
                </ul>
              </div>

              <div className="p-4 bg-green-900/50 border border-green-500/50 rounded-lg">
                <h3 className="font-bold text-green-400 mb-2">✅ 3. Token Card Sell Percentage Button Bug</h3>
                <p className="text-sm text-gray-300 mb-2">
                  <strong>Fixed:</strong> Sell percentage buttons now execute trades directly
                </p>
                <ul className="text-xs text-gray-400 space-y-1">
                  <li>• Fixed 25%, 50%, 75%, 100% buttons to call handleSellTrade directly</li>
                  <li>• Removed unwanted navigation to trading page</li>
                  <li>• Added confirmation dialog for 100% sell (EXIT ALL)</li>
                  <li>• Updated CUSTOM SELL button to open position modal</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Manual Verification Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Manual Verification Required</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="p-4 bg-blue-900/50 border border-blue-500/50 rounded-lg">
                <h3 className="font-bold text-blue-400 mb-2">🔍 1. Test SignalService Fix</h3>
                <p className="text-sm text-gray-300 mb-2">
                  Verify that console errors are resolved:
                </p>
                <ul className="text-xs text-gray-400 space-y-1 mb-3">
                  <li>• Open browser developer tools (F12)</li>
                  <li>• Navigate to Settings page</li>
                  <li>• Check console for any "Error fetching all channels: {}" messages</li>
                  <li>• Verify descriptive error messages if database tables are missing</li>
                </ul>
                <Link href="/settings">
                  <Button variant="outline" size="sm">Test Settings Page</Button>
                </Link>
              </div>

              <div className="p-4 bg-blue-900/50 border border-blue-500/50 rounded-lg">
                <h3 className="font-bold text-blue-400 mb-2">🔍 2. Test Portfolio Display Labels</h3>
                <p className="text-sm text-gray-300 mb-2">
                  Verify the Active Diamond Hands section shows correct labels:
                </p>
                <ul className="text-xs text-gray-400 space-y-1 mb-3">
                  <li>• Check "Initial Investment" instead of "BAG SIZE"</li>
                  <li>• Check "Initial MC" instead of "ENTRY"</li>
                  <li>• Check "Current MC" instead of "CURRENT"</li>
                  <li>• Verify market cap calculations are properly formatted</li>
                </ul>
                <Link href="/portfolio">
                  <Button variant="outline" size="sm">Test Portfolio Page</Button>
                </Link>
              </div>

              <div className="p-4 bg-blue-900/50 border border-blue-500/50 rounded-lg">
                <h3 className="font-bold text-blue-400 mb-2">🔍 3. Test Sell Percentage Buttons</h3>
                <p className="text-sm text-gray-300 mb-2">
                  Verify sell buttons work correctly without unwanted navigation:
                </p>
                <ul className="text-xs text-gray-400 space-y-1 mb-3">
                  <li>• Click 25%, 50%, 75% buttons - should execute sell directly</li>
                  <li>• Click 100% button - should show confirmation dialog</li>
                  <li>• Click CUSTOM SELL - should open position modal</li>
                  <li>• Verify no unwanted navigation to Diamond Hands page</li>
                </ul>
                <Link href="/portfolio">
                  <Button variant="outline" size="sm">Test Sell Buttons</Button>
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Tools */}
        <Card>
          <CardHeader>
            <CardTitle>Additional Tools</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Link href="/setup-database">
                <Button variant="outline" size="sm" className="w-full">Database Setup</Button>
              </Link>
              <Link href="/test-wallet-state">
                <Button variant="outline" size="sm" className="w-full">Wallet Test</Button>
              </Link>
              <Link href="/signals">
                <Button variant="outline" size="sm" className="w-full">Signals Page</Button>
              </Link>
              <Link href="/trading">
                <Button variant="outline" size="sm" className="w-full">Trading Page</Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
