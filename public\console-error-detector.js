/**
 * Console Error Detection and Analysis Script
 * Captures and categorizes all console errors for Signal V1
 */

(function() {
  console.log('🔍 Starting Console Error Detection...');
  
  const errorCategories = {
    react: [],
    network: [],
    javascript: [],
    chunk: [],
    wallet: [],
    supabase: [],
    telegram: [],
    other: []
  };
  
  let totalErrors = 0;
  let originalConsoleError = console.error;
  let originalConsoleWarn = console.warn;
  
  // Override console.error to capture errors
  console.error = function(...args) {
    totalErrors++;
    categorizeError(args, 'error');
    originalConsoleError.apply(console, args);
  };
  
  // Override console.warn to capture warnings
  console.warn = function(...args) {
    categorizeError(args, 'warning');
    originalConsoleWarn.apply(console, args);
  };
  
  function categorizeError(args, level) {
    const message = args.join(' ').toLowerCase();
    const errorInfo = {
      level,
      message: args.join(' '),
      timestamp: new Date().toISOString(),
      stack: new Error().stack
    };
    
    // Categorize by content
    if (message.includes('react') || message.includes('hydration') || message.includes('useeffect') || message.includes('component')) {
      errorCategories.react.push(errorInfo);
    } else if (message.includes('chunk') || message.includes('loading') || message.includes('import')) {
      errorCategories.chunk.push(errorInfo);
    } else if (message.includes('network') || message.includes('fetch') || message.includes('cors') || message.includes('404') || message.includes('500')) {
      errorCategories.network.push(errorInfo);
    } else if (message.includes('wallet') || message.includes('phantom') || message.includes('solflare') || message.includes('solana')) {
      errorCategories.wallet.push(errorInfo);
    } else if (message.includes('supabase') || message.includes('database') || message.includes('auth')) {
      errorCategories.supabase.push(errorInfo);
    } else if (message.includes('telegram') || message.includes('bot') || message.includes('api')) {
      errorCategories.telegram.push(errorInfo);
    } else if (message.includes('undefined') || message.includes('null') || message.includes('cannot read') || message.includes('typeerror')) {
      errorCategories.javascript.push(errorInfo);
    } else {
      errorCategories.other.push(errorInfo);
    }
  }
  
  // Capture unhandled promise rejections
  window.addEventListener('unhandledrejection', function(event) {
    totalErrors++;
    const errorInfo = {
      level: 'unhandled_rejection',
      message: event.reason?.message || String(event.reason),
      timestamp: new Date().toISOString(),
      stack: event.reason?.stack
    };
    
    categorizeError([errorInfo.message], 'unhandled_rejection');
    console.error('Unhandled Promise Rejection:', event.reason);
  });
  
  // Capture global errors
  window.addEventListener('error', function(event) {
    totalErrors++;
    const errorInfo = {
      level: 'global_error',
      message: event.message,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      timestamp: new Date().toISOString(),
      stack: event.error?.stack
    };
    
    categorizeError([errorInfo.message], 'global_error');
  });
  
  // Check for specific React issues
  function checkReactIssues() {
    console.log('🔍 Checking for React-specific issues...');
    
    // Check for hydration mismatches
    const hydrationElements = document.querySelectorAll('[data-reactroot]');
    if (hydrationElements.length === 0) {
      console.warn('⚠️ No React root elements found - possible hydration issue');
    }
    
    // Check for missing keys in lists
    const listElements = document.querySelectorAll('ul, ol');
    listElements.forEach((list, index) => {
      const items = list.children;
      if (items.length > 1) {
        console.log(`📝 List ${index + 1} has ${items.length} items (check for React keys)`);
      }
    });
    
    // Check for potential memory leaks
    if (typeof window.React !== 'undefined') {
      console.log('⚛️ React is available globally');
    }
  }
  
  // Check for network issues
  function checkNetworkIssues() {
    console.log('🌐 Checking for network issues...');
    
    // Check for failed resources
    const images = document.querySelectorAll('img');
    const scripts = document.querySelectorAll('script[src]');
    const links = document.querySelectorAll('link[href]');
    
    let failedResources = 0;
    
    [...images, ...scripts, ...links].forEach(element => {
      if (element.complete === false || element.readyState === 'error') {
        failedResources++;
        console.warn(`❌ Failed to load: ${element.src || element.href}`);
      }
    });
    
    console.log(`📊 Resource check: ${failedResources} failed resources found`);
  }
  
  // Check for wallet issues
  function checkWalletIssues() {
    console.log('👛 Checking for wallet issues...');
    
    // Check if wallet adapters are available
    const walletChecks = [
      'window.phantom',
      'window.solflare',
      'window.backpack',
      'window.glow'
    ];
    
    walletChecks.forEach(walletPath => {
      const wallet = walletPath.split('.').reduce((obj, prop) => obj?.[prop], window);
      if (wallet) {
        console.log(`✅ ${walletPath} is available`);
      } else {
        console.log(`ℹ️ ${walletPath} is not available (wallet not installed)`);
      }
    });
  }
  
  // Generate report
  function generateReport() {
    console.log('\n📊 CONSOLE ERROR ANALYSIS REPORT');
    console.log('=====================================');
    
    let totalCategorizedErrors = 0;
    
    Object.entries(errorCategories).forEach(([category, errors]) => {
      if (errors.length > 0) {
        console.log(`\n❌ ${category.toUpperCase()} ERRORS: ${errors.length}`);
        totalCategorizedErrors += errors.length;
        
        errors.slice(0, 3).forEach((error, index) => {
          console.log(`  ${index + 1}. [${error.level}] ${error.message.slice(0, 100)}...`);
        });
        
        if (errors.length > 3) {
          console.log(`  ... and ${errors.length - 3} more`);
        }
      }
    });
    
    console.log(`\n🎯 SUMMARY:`);
    console.log(`Total Errors Detected: ${totalErrors}`);
    console.log(`Categorized Errors: ${totalCategorizedErrors}`);
    
    // Provide fix suggestions
    console.log(`\n🔧 FIX SUGGESTIONS:`);
    
    if (errorCategories.react.length > 0) {
      console.log(`• React Issues (${errorCategories.react.length}): Check useEffect dependencies, component keys, hydration`);
    }
    
    if (errorCategories.network.length > 0) {
      console.log(`• Network Issues (${errorCategories.network.length}): Check API endpoints, CORS, missing resources`);
    }
    
    if (errorCategories.chunk.length > 0) {
      console.log(`• Chunk Issues (${errorCategories.chunk.length}): Clear cache, restart dev server`);
    }
    
    if (errorCategories.wallet.length > 0) {
      console.log(`• Wallet Issues (${errorCategories.wallet.length}): Check wallet adapter configuration`);
    }
    
    if (errorCategories.javascript.length > 0) {
      console.log(`• JavaScript Issues (${errorCategories.javascript.length}): Check for undefined variables, null references`);
    }
    
    return {
      totalErrors,
      categories: errorCategories,
      summary: Object.fromEntries(
        Object.entries(errorCategories).map(([key, value]) => [key, value.length])
      )
    };
  }
  
  // Run checks
  setTimeout(() => {
    checkReactIssues();
    checkNetworkIssues();
    checkWalletIssues();
    
    const report = generateReport();
    
    // Store report for later analysis
    window.errorReport = report;
    
    console.log('\n✨ Error detection completed!');
    console.log('📋 Access full report with: window.errorReport');
    
  }, 3000); // Wait 3 seconds to capture initial errors
  
})();
