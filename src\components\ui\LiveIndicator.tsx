'use client';

import React, { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';

interface LiveIndicatorProps {
  value: number | string;
  previousValue?: number | string;
  className?: string;
  type?: 'price' | 'percentage' | 'volume' | 'default';
  showPulse?: boolean;
  children?: React.ReactNode;
}

export function LiveIndicator({
  value,
  previousValue,
  className,
  type = 'default',
  showPulse = true,
  children
}: LiveIndicatorProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [changeDirection, setChangeDirection] = useState<'up' | 'down' | 'neutral'>('neutral');

  useEffect(() => {
    if (previousValue !== undefined && previousValue !== value) {
      setIsUpdating(true);
      
      // Determine change direction
      if (typeof value === 'number' && typeof previousValue === 'number') {
        if (value > previousValue) {
          setChangeDirection('up');
        } else if (value < previousValue) {
          setChangeDirection('down');
        } else {
          setChangeDirection('neutral');
        }
      }

      // Reset animation after duration
      const timer = setTimeout(() => {
        setIsUpdating(false);
      }, 800);

      return () => clearTimeout(timer);
    }
  }, [value, previousValue]);

  const getIndicatorClasses = () => {
    const baseClasses = 'transition-all duration-300';
    
    if (!isUpdating) return baseClasses;

    switch (changeDirection) {
      case 'up':
        return cn(baseClasses, 'price-up pulse-data');
      case 'down':
        return cn(baseClasses, 'price-down pulse-data');
      default:
        return cn(baseClasses, 'price-neutral pulse-data');
    }
  };

  const getLiveIndicatorClasses = () => {
    const baseClasses = 'live-indicator';
    
    switch (changeDirection) {
      case 'up':
        return cn(baseClasses, 'green');
      case 'down':
        return cn(baseClasses, 'red');
      default:
        return cn(baseClasses, 'blue');
    }
  };

  return (
    <div className={cn(getLiveIndicatorClasses(), className)}>
      <div className={getIndicatorClasses()}>
        {children || value}
      </div>
      

    </div>
  );
}

interface PulseDotProps {
  color?: 'green' | 'red' | 'blue' | 'gold' | 'purple';
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export function PulseDot({ color = 'green', size = 'md', className }: PulseDotProps) {
  const sizeClasses = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };

  const colorClasses = {
    green: 'bg-green-500',
    red: 'bg-red-500',
    blue: 'bg-neutral-400',
    gold: 'bg-neutral-400',
    purple: 'bg-neutral-400'
  };

  return (
    <div className={cn(
      'rounded-full',
      sizeClasses[size],
      colorClasses[color],
      className
    )} />
  );
}

interface BreathingCardProps {
  children: React.ReactNode;
  className?: string;
  intensity?: 'subtle' | 'normal' | 'strong';
}

export function BreathingCard({ children, className }: BreathingCardProps) {
  return (
    <div className={className}>
      {children}
    </div>
  );
}

interface ShimmerTextProps {
  children: React.ReactNode;
  className?: string;
  speed?: 'slow' | 'normal' | 'fast';
}

export function ShimmerText({ children, className, speed = 'normal' }: ShimmerTextProps) {
  const speedClasses = {
    slow: 'animate-shimmer-slow',
    normal: 'animate-shimmer',
    fast: 'animate-shimmer-fast'
  };

  return (
    <div className={cn(
      'relative overflow-hidden',
      className
    )}>
      {/* Base text layer */}
      <div className="relative">
        {children}
      </div>
      {/* Shimmer overlay */}
      <div className={cn(
        'absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent',
        'bg-[length:200%_100%] mix-blend-overlay',
        speedClasses[speed]
      )} />
    </div>
  );
}
