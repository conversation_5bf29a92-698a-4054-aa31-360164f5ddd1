// Automated Trading Service - Handles signal-based automated trading

import { JupiterTrader } from './jupiterTrader';
import { PriceMonitorService } from '../services/priceMonitor';
import { SignalService } from '../supabase/services/signalService';
import { PortfolioService } from '../supabase/services/portfolioService';
import { UserService } from '../supabase/services/userService';
import { TRADING_CONFIG } from '../constants';
import { calculatePercentageChange } from '../utils';
import type { TelegramSignal, Position, UserPreferences } from '@/types';

export interface AutoTradeSettings {
  enabled: boolean;
  defaultAmount: number;
  maxPositionPercentage: number;
  stopLossPercentage: number;
  profitTakingEnabled: boolean;
  profitTakingLevels: Array<{
    percentage: number;
    sellPercentage: number;
  }>;
}

export class AutoTrader {
  private static instance: AutoTrader;
  private trader: JupiterTrader | null = null;
  private isRunning = false;
  private settings: AutoTradeSettings;
  private processedSignals = new Set<string>();

  private constructor() {
    this.settings = {
      enabled: false,
      defaultAmount: TRADING_CONFIG.defaultTradeAmount,
      maxPositionPercentage: TRADING_CONFIG.maxPositionPercentage,
      stopLossPercentage: TRADING_CONFIG.stopLossPercentage,
      profitTakingEnabled: true,
      profitTakingLevels: TRADING_CONFIG.profitTakingLevels,
    };
  }

  static getInstance(): AutoTrader {
    if (!AutoTrader.instance) {
      AutoTrader.instance = new AutoTrader();
    }
    return AutoTrader.instance;
  }

  /**
   * Initialize auto trader with wallet
   */
  initialize(wallet: any): void {
    this.trader = new JupiterTrader(wallet);

    // Restore running state from localStorage
    this.restoreRunningState();
  }

  /**
   * Start automated trading
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Auto trader is already running');
      return;
    }

    if (!this.trader) {
      throw new Error('Auto trader not initialized with wallet');
    }

    console.log('Starting automated trading...');
    this.isRunning = true;

    // Persist running state
    this.saveRunningState();

    // Load user settings
    await this.loadUserSettings();

    // Start monitoring for new signals
    this.startSignalMonitoring();

    // Start position monitoring for profit taking and stop losses
    this.startPositionMonitoring();
  }

  /**
   * Stop automated trading
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    console.log('Stopping automated trading...');
    this.isRunning = false;

    // Persist running state
    this.saveRunningState();
  }

  /**
   * Update auto trade settings
   */
  async updateSettings(newSettings: Partial<AutoTradeSettings>): Promise<void> {
    this.settings = { ...this.settings, ...newSettings };

    // Persist settings to database
    await this.saveUserSettings();
  }

  /**
   * Save user trading settings to database
   */
  private async saveUserSettings(): Promise<void> {
    try {
      const userProfile = await UserService.getCurrentUserProfile();
      if (userProfile) {
        const updatedPreferences = {
          ...userProfile.preferences,
          trading: {
            ...userProfile.preferences.trading,
            autoTrade: this.settings.enabled,
            defaultAmount: this.settings.defaultAmount,
            maxPositionPercentage: this.settings.maxPositionPercentage,
            stopLossPercentage: this.settings.stopLossPercentage,
            profitTakingEnabled: this.settings.profitTakingEnabled,
          }
        };

        await UserService.updateUserPreferences(updatedPreferences);
        console.log('Auto trader settings saved successfully');
      }
    } catch (error) {
      console.error('Failed to save auto trader settings:', error);
    }
  }

  /**
   * Get current settings
   */
  getSettings(): AutoTradeSettings {
    return { ...this.settings };
  }

  /**
   * Load user trading settings
   */
  private async loadUserSettings(): Promise<void> {
    try {
      const userProfile = await UserService.getCurrentUserProfile();
      if (userProfile?.preferences?.trading) {
        const tradingPrefs = userProfile.preferences.trading;
        this.settings = {
          enabled: tradingPrefs.autoTrade || false,
          defaultAmount: tradingPrefs.defaultAmount || TRADING_CONFIG.defaultTradeAmount,
          maxPositionPercentage: tradingPrefs.maxPositionPercentage || TRADING_CONFIG.maxPositionPercentage,
          stopLossPercentage: tradingPrefs.stopLossPercentage || TRADING_CONFIG.stopLossPercentage,
          profitTakingEnabled: tradingPrefs.profitTakingEnabled || true,
          profitTakingLevels: TRADING_CONFIG.profitTakingLevels,
        };
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
    }
  }

  /**
   * Start monitoring for new signals
   */
  private startSignalMonitoring(): void {
    // Subscribe to new signals
    const subscription = SignalService.subscribeToNewSignals(async (signal) => {
      if (this.isRunning && this.settings.enabled) {
        await this.processNewSignal(signal);
      }
    });

    // Store subscription for cleanup (simplified)
    console.log('Started signal monitoring');
  }

  /**
   * Start monitoring positions for profit taking and stop losses
   */
  private startPositionMonitoring(): void {
    const checkPositions = async () => {
      if (!this.isRunning || !this.settings.enabled) return;

      try {
        const activePositions = await PortfolioService.getActivePositions();
        
        for (const position of activePositions) {
          await this.checkPositionForActions(position);
        }
      } catch (error) {
        console.error('Error monitoring positions:', error);
      }
    };

    // Check positions every minute
    const interval = setInterval(checkPositions, 60000);
    
    // Initial check
    checkPositions();
  }

  /**
   * Process a new trading signal
   */
  private async processNewSignal(signal: TelegramSignal): Promise<void> {
    try {
      // Skip if already processed
      if (this.processedSignals.has(signal.id)) {
        return;
      }

      // Skip if signal is not valid
      if (!signal.valid || !signal.tokenAddress) {
        return;
      }

      console.log(`Processing new signal: ${signal.tokenSymbol || signal.tokenAddress}`);

      // Check if we already have a position in this token
      const activePositions = await PortfolioService.getActivePositions();
      const existingPosition = activePositions.find(p => p.tokenAddress === signal.tokenAddress);
      
      if (existingPosition) {
        console.log(`Already have position in ${signal.tokenSymbol}, skipping`);
        this.processedSignals.add(signal.id);
        return;
      }

      // Execute buy trade
      const result = await this.trader!.buyToken({
        tokenAddress: signal.tokenAddress,
        tokenSymbol: signal.tokenSymbol || 'UNKNOWN',
        amount: this.settings.defaultAmount,
        signalId: signal.id,
      });

      if (result.success) {
        console.log(`✅ Successfully bought ${signal.tokenSymbol}: ${result.signature}`);
        
        // Add token to price monitoring
        PriceMonitorService.getInstance().addToken(signal.tokenAddress);
      } else {
        console.log(`❌ Failed to buy ${signal.tokenSymbol}: ${result.error}`);
      }

      this.processedSignals.add(signal.id);
    } catch (error) {
      console.error('Error processing signal:', error);
    }
  }

  /**
   * Check position for profit taking or stop loss actions
   */
  private async checkPositionForActions(position: Position): Promise<void> {
    try {
      const pnlPercentage = position.pnlPercentage;

      // Check for stop loss
      if (pnlPercentage <= -this.settings.stopLossPercentage) {
        console.log(`🛑 Stop loss triggered for ${position.tokenSymbol} at ${pnlPercentage.toFixed(2)}%`);
        
        const result = await this.trader!.sellToken({
          tokenAddress: position.tokenAddress,
          tokenSymbol: position.tokenSymbol,
          amount: 0, // Will be calculated from position
          percentage: 100, // Sell entire position
        });

        if (result.success) {
          console.log(`✅ Stop loss executed for ${position.tokenSymbol}: ${result.signature}`);
        } else {
          console.log(`❌ Stop loss failed for ${position.tokenSymbol}: ${result.error}`);
        }
        return;
      }

      // Check for profit taking
      if (this.settings.profitTakingEnabled && pnlPercentage > 0) {
        for (const level of this.settings.profitTakingLevels) {
          if (pnlPercentage >= level.percentage) {
            // Check if we've already taken profit at this level
            const profitTaken = await this.hasProfitBeenTaken(position.id, level.percentage);
            
            if (!profitTaken) {
              console.log(`💰 Profit taking triggered for ${position.tokenSymbol} at ${pnlPercentage.toFixed(2)}% (${level.sellPercentage}% sell)`);
              
              const result = await this.trader!.sellToken({
                tokenAddress: position.tokenAddress,
                tokenSymbol: position.tokenSymbol,
                amount: 0, // Will be calculated from position
                percentage: level.sellPercentage,
              });

              if (result.success) {
                console.log(`✅ Profit taking executed for ${position.tokenSymbol}: ${result.signature}`);
                
                // Mark this level as taken (simplified - would need database tracking)
                await this.markProfitTaken(position.id, level.percentage);
              } else {
                console.log(`❌ Profit taking failed for ${position.tokenSymbol}: ${result.error}`);
              }
              
              // Only execute one profit taking level per check
              break;
            }
          }
        }
      }
    } catch (error) {
      console.error(`Error checking position actions for ${position.tokenSymbol}:`, error);
    }
  }

  /**
   * Check if profit has been taken at a specific level
   */
  private async hasProfitBeenTaken(positionId: string, percentage: number): Promise<boolean> {
    // This would need to be implemented with database tracking
    // For now, return false to allow profit taking
    return false;
  }

  /**
   * Mark profit as taken at a specific level
   */
  private async markProfitTaken(positionId: string, percentage: number): Promise<void> {
    // This would need to be implemented with database tracking
    // For now, just log it
    console.log(`Marked profit taken for position ${positionId} at ${percentage}%`);
  }

  /**
   * Get auto trader status
   */
  getStatus(): {
    isRunning: boolean;
    settings: AutoTradeSettings;
    processedSignalsCount: number;
  } {
    return {
      isRunning: this.isRunning,
      settings: this.settings,
      processedSignalsCount: this.processedSignals.size,
    };
  }

  /**
   * Manually execute trade for a signal
   */
  async executeManualTrade(signalId: string, amount?: number): Promise<any> {
    if (!this.trader) {
      throw new Error('Auto trader not initialized');
    }

    try {
      // Get signal details
      const signals = await SignalService.getRecentSignals(100);
      const signal = signals.find(s => s.id === signalId);

      if (!signal || !signal.valid || !signal.tokenAddress) {
        throw new Error('Invalid signal');
      }

      // Execute trade
      const result = await this.trader.buyToken({
        tokenAddress: signal.tokenAddress,
        tokenSymbol: signal.tokenSymbol || 'UNKNOWN',
        amount: amount || this.settings.defaultAmount,
        signalId: signal.id,
      });

      if (result.success) {
        // Add token to price monitoring
        PriceMonitorService.getInstance().addToken(signal.tokenAddress);
      }

      return result;
    } catch (error) {
      console.error('Error executing manual trade:', error);
      throw error;
    }
  }

  /**
   * Save running state to localStorage for persistence across sessions
   */
  private saveRunningState(): void {
    try {
      localStorage.setItem('signal-v1-auto-trader-running', JSON.stringify(this.isRunning));
    } catch (error) {
      console.error('Failed to save auto trader running state:', error);
    }
  }

  /**
   * Restore running state from localStorage
   */
  private restoreRunningState(): void {
    try {
      const savedState = localStorage.getItem('signal-v1-auto-trader-running');
      if (savedState !== null) {
        const wasRunning = JSON.parse(savedState);
        if (wasRunning && this.trader) {
          // Auto-start if it was running before
          console.log('Restoring auto trader running state...');
          this.start().catch(error => {
            console.error('Failed to restore auto trader running state:', error);
          });
        }
      }
    } catch (error) {
      console.error('Failed to restore auto trader running state:', error);
    }
  }
}
