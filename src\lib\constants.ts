// Signal V1 - Application Constants

export const APP_CONFIG = {
  name: 'Signal V1',
  description: 'Comprehensive mobile-first trading application',
  version: '0.1.0',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
} as const;

export const SOLANA_CONFIG = {
  network: process.env.NEXT_PUBLIC_SOLANA_NETWORK || 'mainnet-beta',
  rpcUrl: process.env.NEXT_PUBLIC_SOLANA_RPC_URL || 'https://api.mainnet-beta.solana.com',
  commitment: 'confirmed',
} as const;

export const JUPITER_CONFIG = {
  apiUrl: process.env.NEXT_PUBLIC_JUPITER_API_URL || 'https://quote-api.jup.ag/v6',
  slippageBps: 300, // 3% default slippage
  maxAccounts: 64,
} as const;

export const TRADING_CONFIG = {
  defaultTradeAmount: parseFloat(process.env.DEFAULT_TRADE_AMOUNT_SOL || '0.1'),
  maxPositionPercentage: parseFloat(process.env.MAX_POSITION_PERCENTAGE || '5'),
  stopLossPercentage: parseFloat(process.env.STOP_LOSS_PERCENTAGE || '30'),
  maxActivePositions: parseInt(process.env.MAX_ACTIVE_POSITIONS || '10'),
  profitTakingLevels: [
    { percentage: 100, sellPercentage: 50 }, // 2x - sell 50%
    { percentage: 200, sellPercentage: 25 }, // 3x - sell 25%
    { percentage: 400, sellPercentage: 15 }, // 5x - sell 15%
  ],
} as const;

export const TELEGRAM_CONFIG = {
  // User Account Authentication (replaces Bot API)
  apiId: process.env.TELEGRAM_API_ID,
  apiHash: process.env.TELEGRAM_API_HASH,
  sessionString: process.env.TELEGRAM_SESSION_STRING,
  phoneNumber: process.env.TELEGRAM_PHONE_NUMBER,
  // Legacy bot config (deprecated)
  botToken: process.env.TELEGRAM_BOT_TOKEN,
  webhookUrl: process.env.TELEGRAM_WEBHOOK_URL,
  signalTriggers: [
    '🚨Alerted at',
    '🛎️First Hit',
    'SIGNAL:',
    'BUY:',
    'ENTRY:',
  ],
  communityLinks: {
    // Replace with actual Telegram community links
    mainGroup: 'https://t.me/signalv1community',
    supportGroup: 'https://t.me/signalv1support',
    announcementChannel: 'https://t.me/signalv1announcements',
  },
} as const;

export const SUPABASE_CONFIG = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY,
} as const;

export const API_ENDPOINTS = {
  dexScreener: process.env.DEXSCREENER_API_URL || 'https://api.dexscreener.com/latest',
  jupiter: JUPITER_CONFIG.apiUrl,
} as const;

export const UI_CONFIG = {
  breakpoints: {
    mobile: '320px',
    tablet: '768px',
    desktop: '1024px',
  },
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500,
    },
  },
  colors: {
    primary: '#10B981', // Emerald
    secondary: '#3B82F6', // Blue
    success: '#22C55E', // Green
    warning: '#F59E0B', // Amber
    error: '#EF4444', // Red
    info: '#06B6D4', // Cyan
  },
} as const;

export const VALIDATION_RULES = {
  solanaAddress: {
    length: 44,
    pattern: /^[1-9A-HJ-NP-Za-km-z]{32,44}$/,
  },
  tradeAmount: {
    min: 0.001,
    max: 100,
  },
  slippage: {
    min: 0.1,
    max: 50,
  },
} as const;

export const STORAGE_KEYS = {
  walletAdapter: 'walletAdapter',
  userPreferences: 'userPreferences',
  tradingSettings: 'tradingSettings',
  signalHistory: 'signalHistory',
  portfolioData: 'portfolioData',
} as const;

export const ERROR_MESSAGES = {
  wallet: {
    notConnected: 'Please connect your wallet to continue',
    insufficientBalance: 'Insufficient balance for this transaction',
    transactionFailed: 'Transaction failed. Please try again.',
  },
  trading: {
    invalidAmount: 'Please enter a valid trade amount',
    maxPositionsReached: 'Maximum number of active positions reached',
    tokenNotFound: 'Token not found or invalid contract address',
  },
  telegram: {
    connectionFailed: 'Failed to connect to Telegram',
    invalidSignal: 'Invalid signal format detected',
    channelNotFound: 'Telegram channel not found',
  },
  general: {
    networkError: 'Network error. Please check your connection.',
    unknownError: 'An unexpected error occurred',
    rateLimitExceeded: 'Rate limit exceeded. Please try again later.',
  },
} as const;
