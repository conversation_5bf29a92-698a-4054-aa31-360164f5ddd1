// Degen Terminology Mapping - Crypto-native language for the trenches
// Converts standard trading terms to degen slang

export const DEGEN_TERMS = {
  // Core Trading Terms
  portfolio: 'Bag',
  holdings: 'Holdings',
  trade: 'Send It',
  buy: 'Ape In',
  sell: 'Take Profits',
  profit: 'Gains',
  loss: 'Rekt',
  gains: 'Moon',
  losses: 'Down Bad',
  
  // Signal Terms
  signal: 'Alpha',
  buySignal: 'Send It Signal',
  sellSignal: 'Exit Signal',
  strongSignal: 'Full Send',
  weakSignal: 'Maybe Send',
  
  // Risk Management
  riskManagement: "Don't Get Rekt",
  stopLoss: 'Stop Rekt',
  takeProfit: 'Secure Bag',
  positionSize: 'Bag Size',
  
  // Trading Actions
  autoTrading: 'Diamond Hands Mode',
  manualTrading: 'Manual Send',
  execute: 'Send It',
  cancel: 'No Send',
  
  // Portfolio States
  totalValue: 'Total Bag Value',
  totalPnL: 'Total Gains/Rekt',
  activePositions: 'Active Bags',
  
  // Market Terms
  pump: 'Pump',
  dump: 'Dump',
  moon: 'Moon',
  lambo: 'Lambo',
  rekt: 'Rekt',
  diamond: 'Diamond',
  paper: 'Paper',
  
  // Multipliers
  '2x': '2x (Double Up)',
  '5x': '5x (Moon Mission)',
  '10x': '10x (Lambo Time)',
  
  // Status Terms
  connected: 'Connected to the Matrix',
  disconnected: 'Offline (Touch Grass)',
  loading: 'Loading Alpha...',
  error: 'Something Went Rekt',
  success: 'Mission Success',
  
  // Time Terms
  '24h': '24h',
  '7d': '7d',
  '30d': '30d',
  'all': 'Since Genesis',
  
  // Amounts
  balance: 'Bag Balance',
  available: 'Available to Send',
  locked: 'Diamond Handed',
} as const;

export const DEGEN_EMOJIS = {
  // Core emojis
  rocket: '🚀',
  diamond: '💎',
  fire: '🔥',
  chart: '📈',
  chartDown: '📉',
  money: '💰',
  gem: '💎',
  explosion: '💥',
  lightning: '⚡',
  target: '🎯',
  
  // Status emojis
  success: '✅',
  error: '❌',
  warning: '⚠️',
  info: 'ℹ️',
  loading: '⏳',
  
  // Trading emojis
  buy: '🟢',
  sell: '🔴',
  hold: '🟡',
  moon: '🌙',
  lambo: '🏎️',
  
  // Degen culture
  ape: '🦍',
  brain: '🧠',
  eyes: '👀',
  hands: '🙌',
  strong: '💪',
} as const;

// Utility functions for terminology
export function getDegenTerm(standardTerm: keyof typeof DEGEN_TERMS): string {
  return DEGEN_TERMS[standardTerm] || standardTerm;
}

export function formatDegenAmount(amount: number, symbol: string = 'SOL'): string {
  if (amount >= 1000000) {
    return `${(amount / 1000000).toFixed(2)}M ${symbol} ${DEGEN_EMOJIS.rocket}`;
  } else if (amount >= 1000) {
    return `${(amount / 1000).toFixed(2)}K ${symbol} ${DEGEN_EMOJIS.fire}`;
  } else {
    return `${amount.toFixed(4)} ${symbol}`;
  }
}

export function formatDegenPercentage(percentage: number): string {
  const emoji = percentage > 0 ? DEGEN_EMOJIS.rocket : DEGEN_EMOJIS.chartDown;
  const term = percentage > 0 ? 'Moon' : 'Rekt';
  
  if (Math.abs(percentage) >= 100) {
    return `${percentage > 0 ? '+' : ''}${percentage.toFixed(1)}% ${term} ${emoji}`;
  } else {
    return `${percentage > 0 ? '+' : ''}${percentage.toFixed(2)}% ${emoji}`;
  }
}

export function getDegenMultiplier(multiplier: number): string {
  // Simplified format for multiplier badges - just show the number
  if (multiplier >= 1) {
    return `${multiplier.toFixed(1)}x`;
  } else {
    return `${multiplier.toFixed(2)}x`;
  }
}

export function getDegenStatus(status: 'profit' | 'loss' | 'neutral'): {
  text: string;
  emoji: string;
  color: string;
} {
  switch (status) {
    case 'profit':
      return {
        text: 'Moon Mission',
        emoji: DEGEN_EMOJIS.rocket,
        color: 'text-degen-green',
      };
    case 'loss':
      return {
        text: 'Down Bad',
        emoji: DEGEN_EMOJIS.chartDown,
        color: 'text-degen-red',
      };
    default:
      return {
        text: 'Crab Market',
        emoji: DEGEN_EMOJIS.chart,
        color: 'text-degen-blue',
      };
  }
}

export function getDegenGreeting(): string {
  const greetings = [
    `Welcome to the Trenches ${DEGEN_EMOJIS.fire}`,
    `GM Degen ${DEGEN_EMOJIS.rocket}`,
    `Ready to Send It? ${DEGEN_EMOJIS.diamond}`,
    `Time to Ape In ${DEGEN_EMOJIS.ape}`,
    `Diamond Hands Only ${DEGEN_EMOJIS.hands}`,
    `Moon Mission Loading ${DEGEN_EMOJIS.moon}`,
  ];
  
  return greetings[Math.floor(Math.random() * greetings.length)];
}

export function getDegenLoadingText(): string {
  const loadingTexts = [
    `Loading Alpha... ${DEGEN_EMOJIS.loading}`,
    `Scanning for Gems... ${DEGEN_EMOJIS.gem}`,
    `Preparing Moon Mission... ${DEGEN_EMOJIS.rocket}`,
    `Checking the Trenches... ${DEGEN_EMOJIS.eyes}`,
    `Diamond Hands Activating... ${DEGEN_EMOJIS.diamond}`,
  ];
  
  return loadingTexts[Math.floor(Math.random() * loadingTexts.length)];
}
