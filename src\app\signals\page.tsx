'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { LiveIndicator, PulseDot, BreathingCard, ShimmerText } from '@/components/ui/LiveIndicator';
import { FlameIcon } from '@/components/ui/FlameIcon';
import { TokenCard } from '@/components/trading/TokenCard';
import { useSignals } from '@/hooks/useSupabase';
import { useStaggeredEntrance } from '@/hooks/useAnimations';
import { useTrading } from '@/hooks/useTrading';
import { useRouter } from 'next/navigation';
import { formatRelativeTime, formatCurrency } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import type { TelegramSignal } from '@/types';

export default function SignalsPage() {
  const { signals, loading, error, refetch } = useSignals(100);
  const { executeBuyTrade, executeSellTrade, loading: tradingLoading } = useTrading();
  const router = useRouter();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterValid, setFilterValid] = useState<boolean | null>(null);
  const [selectedChannel, setSelectedChannel] = useState<string | null>(null);
  const { containerRef, isVisible } = useStaggeredEntrance(6, 100);

  // Handle trading actions
  const handleTrade = async (signal: TelegramSignal, action: 'buy' | 'sell') => {
    try {
      console.log(`${action.toUpperCase()} ${signal.tokenSymbol}`, signal);

      // Navigate to trading page with pre-filled data
      const params = new URLSearchParams({
        token: signal.tokenAddress,
        symbol: signal.tokenSymbol || 'UNKNOWN',
        action: action,
        source: 'signal'
      });

      router.push(`/trading?${params.toString()}`);
    } catch (error) {
      console.error('Trade navigation error:', error);
      alert('Failed to navigate to trading page. Please try again.');
    }
  };

  // Use real signals data from Supabase
  const displaySignals = signals || [];

  // Filter signals based on search and filters
  const filteredSignals = displaySignals.filter(signal => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      if (!signal.tokenSymbol?.toLowerCase().includes(query) &&
          !signal.tokenAddress.toLowerCase().includes(query) &&
          !signal.channelName.toLowerCase().includes(query)) {
        return false;
      }
    }

    if (filterValid !== null && signal.valid !== filterValid) {
      return false;
    }

    if (selectedChannel && signal.channelId !== selectedChannel) {
      return false;
    }

    return true;
  });

  // Get unique channels
  const channels = Array.from(new Set(displaySignals.map(s => s.channelName)))
    .map(name => ({
      id: displaySignals.find(s => s.channelName === name)?.channelId || '',
      name,
    }));

  return (
    <MainLayout>
      <div className="space-y-6" ref={containerRef}>
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between slide-up-enter">
          <div>
            <ShimmerText speed="slow">
              <h1 className="text-4xl font-bold text-primary">
                Live Alpha Signals
              </h1>
            </ShimmerText>
            <p className="mt-3 text-lg text-degen-purple font-medium fade-in-enter">
              Real-time alpha from the trenches - Fresh signals incoming
            </p>
            <div className="flex items-center gap-2 mt-2">
              <PulseDot color="purple" size="sm" />
              <PulseDot color="blue" size="sm" />
              <PulseDot color="green" size="sm" />
            </div>
          </div>

          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <BreathingCard intensity="strong">
              <Badge variant="alpha" className="animate-pulse glow-purple border-gradient-purple glow-pulse-effect">
                <Icon name="signals" size="sm" color="white" className="mr-1" />
                LIVE ALPHA
              </Badge>
            </BreathingCard>
            <BreathingCard intensity="normal">
              <Button onClick={refetch} size="sm" variant="diamond" className="font-bold glow-purple">
                <Icon name="refresh" size="sm" color="white" className="mr-1" />
                Refresh
              </Button>
            </BreathingCard>
          </div>
        </div>

        {/* Alpha Filters */}
        <Card className="bg-gradient-sophisticated border-gradient-purple">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              {/* Search */}
              <Input
                placeholder="Search alpha signals..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="bg-gradient-neutral-subtle border-gradient-blue text-degen-blue placeholder:text-degen-gray"
                leftIcon={
                  <svg className="w-4 h-4 text-degen-blue" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                }
              />

              {/* Channel Filter */}
              <select
                className="h-10 w-full rounded-md bg-gradient-signal-subtle border-gradient-purple px-3 py-2 text-sm text-degen-purple font-medium"
                value={selectedChannel || ''}
                onChange={(e) => setSelectedChannel(e.target.value || null)}
              >
                <option value="">All Alpha Sources</option>
                {channels.map(channel => (
                  <option key={channel.id} value={channel.id}>
                    {channel.name}
                  </option>
                ))}
              </select>

              {/* Valid Filter */}
              <select
                className="h-10 w-full rounded-md bg-gradient-profit-subtle border-gradient-green px-3 py-2 text-sm text-degen-green font-medium"
                value={filterValid === null ? '' : filterValid.toString()}
                onChange={(e) => setFilterValid(e.target.value === '' ? null : e.target.value === 'true')}
              >
                <option value="">All Alpha</option>
                <option value="true">Valid Alpha Only</option>
                <option value="false">Sus Signals</option>
              </select>

              {/* Results Count */}
              <div className="flex items-center text-sm text-degen-gold font-bold bg-gradient-premium-subtle p-3 rounded-lg">
                {filteredSignals.length} of {displaySignals.length} signals
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Signals List */}
        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(6)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card className="bg-gradient-loss border-2 border-degen-red/50 glow-red">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4 animate-bounce">
                {DEGEN_EMOJIS.error}
              </div>
              <h3 className="text-xl font-bold text-white mb-2 flex items-center justify-center gap-2">
                {DEGEN_EMOJIS.chartDown} Alpha Feed Rekt {DEGEN_EMOJIS.chartDown}
              </h3>
              <p className="text-white mb-4">
                Something went wrong loading the alpha: {error}
              </p>
              <Button onClick={refetch} variant="moon" className="font-bold">
                {DEGEN_EMOJIS.target} Try Again
              </Button>
            </CardContent>
          </Card>
        ) : filteredSignals.length === 0 ? (
          <Card className="bg-gradient-degen border-degen-blue/30">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4 animate-float">
                {DEGEN_EMOJIS.eyes}
              </div>
              <h3 className="text-xl font-bold text-degen-blue mb-2 flex items-center justify-center gap-2">
                {DEGEN_EMOJIS.chartDown} No Alpha Found
              </h3>
              <p className="text-degen-gray">
                {searchQuery || filterValid !== null || selectedChannel
                  ? `No alpha matches your filters, anon ${DEGEN_EMOJIS.eyes}`
                  : `No signals detected yet. Alpha scanners are watching the trenches ${DEGEN_EMOJIS.lightning}`}
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6" data-stagger>
            {filteredSignals.map((signal, index) => (
              <BreathingCard key={signal.id} intensity="subtle" className="scale-in-enter" style={{ animationDelay: `${index * 100}ms` }}>
                <TokenCard
                  tokenAddress={signal.tokenAddress}
                  tokenSymbol={signal.tokenSymbol || 'UNKNOWN'}
                  tokenName={signal.tokenSymbol || 'Unknown Token'}
                  price={Math.random() * 0.01 + 0.001} // Mock price
                  priceChange24h={(Math.random() - 0.5) * 200} // Mock 24h change
                  marketCap={Math.random() * 1000000 + 100000} // Mock market cap
                  volume24h={Math.random() * 500000 + 50000} // Mock volume
                  multiplierFromSignal={Math.random() * 15 + 2} // Mock multiplier
                  signalDate={new Date(signal.timestamp)}
                  onTrade={(action) => handleTrade(signal, action)}
                  className="h-full"
                />
              </BreathingCard>
            ))}
          </div>
        )}
      </div>
    </MainLayout>
  );
}
