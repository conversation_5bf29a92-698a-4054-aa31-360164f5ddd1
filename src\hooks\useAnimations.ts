import { useEffect, useRef, useCallback, useState } from 'react';
import { animationUtils, priceChangeAnimation } from '@/lib/animations';

/**
 * Hook for managing price change animations
 */
export function usePriceAnimation(currentValue: number, previousValue?: number) {
  const [animationClass, setAnimationClass] = useState('');
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (previousValue !== undefined && previousValue !== currentValue) {
      const changeClass = priceChangeAnimation.getChangeClass(currentValue, previousValue);
      setAnimationClass(changeClass);

      // Clear animation after duration
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      
      timeoutRef.current = setTimeout(() => {
        setAnimationClass('');
      }, 800);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [currentValue, previousValue]);

  return animationClass;
}

/**
 * Hook for managing data update animations
 */
export function useDataUpdateAnimation() {
  const elementRef = useRef<HTMLElement>(null);

  const triggerUpdate = useCallback(() => {
    if (elementRef.current) {
      animationUtils.triggerDataUpdate(elementRef.current);
    }
  }, []);

  return { elementRef, triggerUpdate };
}

/**
 * Hook for managing entrance animations with staggered delays
 */
export function useStaggeredEntrance(itemCount: number, baseDelay: number = 100) {
  const [isVisible, setIsVisible] = useState(false);
  const containerRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting && !isVisible) {
          setIsVisible(true);
          
          if (containerRef.current) {
            const elements = containerRef.current.querySelectorAll('[data-stagger]');
            animationUtils.staggerElements(elements, baseDelay);
          }
        }
      },
      { threshold: 0.1 }
    );

    if (containerRef.current) {
      observer.observe(containerRef.current);
    }

    return () => observer.disconnect();
  }, [isVisible, baseDelay]);

  return { containerRef, isVisible };
}

/**
 * Hook for managing hover animations
 */
export function useHoverAnimation() {
  const [isHovered, setIsHovered] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  const handleMouseEnter = useCallback(() => {
    setIsHovered(true);
  }, []);

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false);
  }, []);

  useEffect(() => {
    const element = elementRef.current;
    if (element) {
      element.addEventListener('mouseenter', handleMouseEnter);
      element.addEventListener('mouseleave', handleMouseLeave);

      return () => {
        element.removeEventListener('mouseenter', handleMouseEnter);
        element.removeEventListener('mouseleave', handleMouseLeave);
      };
    }
  }, [handleMouseEnter, handleMouseLeave]);

  return { elementRef, isHovered };
}

/**
 * Hook for managing loading animations
 */
export function useLoadingAnimation(isLoading: boolean, type: 'shimmer' | 'pulse' | 'spin' = 'shimmer') {
  const [animationClass, setAnimationClass] = useState('');

  useEffect(() => {
    if (isLoading) {
      switch (type) {
        case 'shimmer':
          setAnimationClass('loading-shimmer');
          break;
        case 'pulse':
          setAnimationClass('loading-pulse');
          break;
        case 'spin':
          setAnimationClass('animate-spin');
          break;
      }
    } else {
      setAnimationClass('');
    }
  }, [isLoading, type]);

  return animationClass;
}

/**
 * Hook for managing live indicator animations
 */
export function useLiveIndicator(isLive: boolean, color: 'green' | 'red' | 'blue' = 'green') {
  const [indicatorClass, setIndicatorClass] = useState('');

  useEffect(() => {
    if (isLive) {
      setIndicatorClass(`live-indicator ${color} glow-pulse-effect`);
    } else {
      setIndicatorClass('');
    }
  }, [isLive, color]);

  return indicatorClass;
}

/**
 * Hook for managing card interaction animations
 */
export function useCardAnimation() {
  const [isInteracting, setIsInteracting] = useState(false);
  const cardRef = useRef<HTMLElement>(null);

  const handleInteractionStart = useCallback(() => {
    setIsInteracting(true);
  }, []);

  const handleInteractionEnd = useCallback(() => {
    setIsInteracting(false);
  }, []);

  useEffect(() => {
    const card = cardRef.current;
    if (card) {
      card.addEventListener('mousedown', handleInteractionStart);
      card.addEventListener('mouseup', handleInteractionEnd);
      card.addEventListener('mouseleave', handleInteractionEnd);

      return () => {
        card.removeEventListener('mousedown', handleInteractionStart);
        card.removeEventListener('mouseup', handleInteractionEnd);
        card.removeEventListener('mouseleave', handleInteractionEnd);
      };
    }
  }, [handleInteractionStart, handleInteractionEnd]);

  const cardClasses = `interactive-card ${isInteracting ? 'scale-95' : ''}`;

  return { cardRef, cardClasses, isInteracting };
}

/**
 * Hook for managing smooth state transitions
 */
export function useStateTransition<T>(
  state: T,
  transitionDuration: number = 300
) {
  const [displayState, setDisplayState] = useState(state);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const timeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (state !== displayState) {
      setIsTransitioning(true);

      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }

      timeoutRef.current = setTimeout(() => {
        setDisplayState(state);
        setIsTransitioning(false);
      }, transitionDuration / 2);
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, [state, displayState, transitionDuration]);

  return { displayState, isTransitioning };
}

/**
 * Hook for managing performance-optimized animations
 */
export function usePerformantAnimation(
  shouldAnimate: boolean,
  animationClass: string,
  duration: number = 300
) {
  const [isAnimating, setIsAnimating] = useState(false);
  const elementRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (shouldAnimate && elementRef.current) {
      setIsAnimating(true);
      elementRef.current.classList.add(animationClass, 'transform-gpu');

      const timer = setTimeout(() => {
        setIsAnimating(false);
        if (elementRef.current) {
          elementRef.current.classList.remove(animationClass);
        }
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [shouldAnimate, animationClass, duration]);

  return { elementRef, isAnimating };
}

/**
 * Hook for managing reduced motion preferences
 */
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}
