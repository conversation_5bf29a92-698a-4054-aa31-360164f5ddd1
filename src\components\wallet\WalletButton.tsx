'use client';

import React, { useState } from 'react';
import { WalletMultiButton } from '@solana/wallet-adapter-react-ui';
import { useWallet } from '@/hooks/useWallet';
import { Button } from '@/components/ui/Button';
import { WalletConnectionModal } from './WalletConnectionModal';
import { formatSOL } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface WalletButtonProps {
  className?: string;
  showBalance?: boolean;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
}

export function WalletButton({
  className,
  showBalance = false,
  variant = 'default',
  size = 'md'
}: WalletButtonProps) {
  const { connected, connecting, balance, shortAddress, disconnect, error, isRestoring } = useWallet();
  const [showModal, setShowModal] = useState(false);

  const handleConnect = () => {
    setShowModal(true);
  };

  const handleModalConnect = () => {
    setShowModal(false);
  };

  if (!connected) {
    return (
      <>
        <div className="space-y-2">
          <Button
            onClick={handleConnect}
            disabled={connecting || isRestoring}
            variant={variant}
            size={size}
            className={cn('min-w-[120px]', className)}
          >
            {connecting
              ? 'Connecting...'
              : isRestoring
                ? 'Restoring...'
                : 'Connect Wallet'
            }
          </Button>
          {error && (
            <div className="text-sm text-red-500 max-w-xs">
              {error}
            </div>
          )}
        </div>

        <WalletConnectionModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          onConnect={handleModalConnect}
        />
      </>
    );
  }

  return (
    <div className={cn('flex items-center gap-2', className)}>
      {showBalance && balance && (
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {formatSOL(balance.sol)}
        </div>
      )}
      
      <Button
        onClick={disconnect}
        variant={variant}
        size={size}
        className="min-w-[120px]"
      >
        {shortAddress}
      </Button>
    </div>
  );
}

// Alternative using the default Solana wallet button
export function DefaultWalletButton({ className }: { className?: string }) {
  return (
    <WalletMultiButton 
      className={cn(
        'wallet-adapter-button-trigger',
        'bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg',
        'transition-colors duration-200',
        className
      )}
    />
  );
}
