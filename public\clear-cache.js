/**
 * Cache Clearing Script for Signal V1
 * Clears all browser caches, service worker caches, and localStorage
 */

(function() {
  console.log('🧹 Starting comprehensive cache clearing...');

  // Clear localStorage
  try {
    localStorage.clear();
    console.log('✅ localStorage cleared');
  } catch (e) {
    console.warn('⚠️ Could not clear localStorage:', e);
  }

  // Clear sessionStorage
  try {
    sessionStorage.clear();
    console.log('✅ sessionStorage cleared');
  } catch (e) {
    console.warn('⚠️ Could not clear sessionStorage:', e);
  }

  // Clear IndexedDB
  if ('indexedDB' in window) {
    try {
      indexedDB.databases().then(databases => {
        databases.forEach(db => {
          if (db.name) {
            indexedDB.deleteDatabase(db.name);
            console.log(`✅ IndexedDB database "${db.name}" cleared`);
          }
        });
      });
    } catch (e) {
      console.warn('⚠️ Could not clear IndexedDB:', e);
    }
  }

  // Clear Service Worker caches
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      console.log(`🗂️ Found ${cacheNames.length} cache(s):`, cacheNames);
      
      return Promise.all(
        cacheNames.map(cacheName => {
          console.log(`🗑️ Deleting cache: ${cacheName}`);
          return caches.delete(cacheName);
        })
      );
    }).then(() => {
      console.log('✅ All service worker caches cleared');
    }).catch(e => {
      console.warn('⚠️ Could not clear service worker caches:', e);
    });
  }

  // Unregister service workers
  if ('serviceWorker' in navigator) {
    navigator.serviceWorker.getRegistrations().then(registrations => {
      console.log(`🔧 Found ${registrations.length} service worker(s)`);
      
      registrations.forEach(registration => {
        registration.unregister().then(() => {
          console.log('✅ Service worker unregistered:', registration.scope);
        }).catch(e => {
          console.warn('⚠️ Could not unregister service worker:', e);
        });
      });
    });
  }

  // Clear cookies for current domain
  try {
    document.cookie.split(";").forEach(function(c) { 
      document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
    });
    console.log('✅ Cookies cleared');
  } catch (e) {
    console.warn('⚠️ Could not clear cookies:', e);
  }

  console.log('🎉 Cache clearing completed! Please refresh the page.');
  
  // Auto-refresh after a short delay
  setTimeout(() => {
    console.log('🔄 Auto-refreshing page...');
    window.location.reload(true);
  }, 2000);
})();
