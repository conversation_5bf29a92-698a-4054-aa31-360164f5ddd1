'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';

import { Input } from '@/components/ui/Input';
import { Modal } from '@/components/ui/Modal';
import { LiveIndicator, PulseDot, BreathingCard, ShimmerText } from '@/components/ui/LiveIndicator';
import { useWallet } from '@/hooks/useWallet';
import { useTrading, useAutoTrader } from '@/hooks/useTrading';
import { usePriceMonitor } from '@/hooks/usePriceMonitor';
import { useSearchParams } from 'next/navigation';
import { formatSOL, formatCurrency, isValidSolanaAddress } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';

export default function TradingPage() {
  const { connected, balance } = useWallet();
  const { executeBuyTrade, executeSellTrade, loading: tradingLoading, error: tradingError } = useTrading();
  const { isRunning: autoTraderRunning, settings: autoTraderSettings, start: startAutoTrader, stop: stopAutoTrader } = useAutoTrader();
  const { isRunning: priceMonitorRunning, start: startPriceMonitor, stop: stopPriceMonitor } = usePriceMonitor();
  const searchParams = useSearchParams();

  // Initialize state from URL parameters
  const urlToken = searchParams.get('token') || '';
  const urlSymbol = searchParams.get('symbol') || '';
  const urlAction = searchParams.get('action') as 'buy' | 'sell' || 'buy';
  const urlAmount = searchParams.get('amount') || '0.1';
  const urlPercentage = searchParams.get('percentage');
  const urlMaxAmount = searchParams.get('maxAmount');
  const urlSource = searchParams.get('source');

  const [showTradeModal, setShowTradeModal] = useState(false);
  const [tradeType, setTradeType] = useState<'buy' | 'sell'>(urlAction);
  const [tokenAddress, setTokenAddress] = useState(urlToken);
  const [tokenSymbol, setTokenSymbol] = useState(urlSymbol);
  const [tradeAmount, setTradeAmount] = useState(urlAmount);
  const [slippage, setSlippage] = useState('3');
  const [isPortfolioSell, setIsPortfolioSell] = useState(urlSource === 'portfolio');
  const [sellPercentage, setSellPercentage] = useState(urlPercentage ? parseInt(urlPercentage) : 100);
  const [maxSellAmount, setMaxSellAmount] = useState(urlMaxAmount ? parseFloat(urlMaxAmount) : 0);

  const handleTrade = async () => {
    if (!connected) {
      alert('Please connect your wallet first');
      return;
    }

    if (!isValidSolanaAddress(tokenAddress)) {
      alert('Please enter a valid Solana token address');
      return;
    }

    const amount = parseFloat(tradeAmount);
    if (isNaN(amount) || amount <= 0) {
      alert('Please enter a valid trade amount');
      return;
    }

    try {
      const params = {
        tokenAddress,
        tokenSymbol: tokenSymbol || 'UNKNOWN',
        amount,
        slippageBps: parseFloat(slippage) * 100,
      };

      let result;
      if (tradeType === 'buy') {
        result = await executeBuyTrade(params);
      } else {
        result = await executeSellTrade(params);
      }

      if (result.success) {
        alert(`Trade successful! Signature: ${result.signature}`);
        setShowTradeModal(false);
        // Reset form
        setTokenAddress('');
        setTokenSymbol('');
        setTradeAmount('0.1');
      } else {
        alert(`Trade failed: ${result.error}`);
      }
    } catch (error) {
      console.error('Trade error:', error);
      alert('Trade failed. Please try again.');
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between slide-up-enter">
          <div>
            <ShimmerText>
              <h1 className="text-4xl font-bold text-primary">
                Diamond Hands Mode
              </h1>
            </ShimmerText>
            <p className="mt-3 text-lg text-degen-blue font-medium fade-in-enter">
              Automated aping based on alpha signals
            </p>
          </div>

          <div className="mt-4 sm:mt-0 flex items-center space-x-3">
            <button
              onClick={() => setShowTradeModal(true)}
              disabled={!connected}
              className="bg-green-500 hover:bg-green-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg font-bold transition-all duration-200 hover:shadow-lg hover:shadow-green-500/30"
              title="Open manual trading interface"
            >
              Manual Trade
            </button>
          </div>
        </div>

        {/* Connection Status */}
        {!connected && (
          <div className="bg-gray-900 border-2 border-red-500/60 rounded-2xl p-6 text-center hover:border-red-400 hover:shadow-lg hover:shadow-red-500/20 transition-all duration-300">
            <div className="text-6xl mb-4 animate-bounce">
              {DEGEN_EMOJIS.warning}
            </div>
            <h3 className="text-xl font-bold text-white mb-2 flex items-center justify-center gap-2">
              {DEGEN_EMOJIS.error} Not Connected to the Matrix {DEGEN_EMOJIS.error}
            </h3>
            <p className="text-gray-400">
              Connect your wallet to start trading {DEGEN_EMOJIS.rocket} No wallet = No gains {DEGEN_EMOJIS.chartDown}
            </p>
          </div>
        )}

        {connected && (
          <>
            {/* Trading Status */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6" data-stagger>
              <BreathingCard intensity="normal">
                <div className="bg-gray-900 border-2 border-green-500/60 rounded-2xl p-4 hover:border-green-400 hover:shadow-lg hover:shadow-green-500/20 transition-all duration-300 relative overflow-hidden">
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                    <div className="text-sm font-medium text-green-400 flex items-center gap-2 mb-3">
                      <PulseDot color="green" size="sm" />
                      {DEGEN_EMOJIS.diamond} Diamond Hands Mode
                    </div>
                    <div className="flex items-center justify-between">
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${autoTraderRunning ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'}`}>
                        {autoTraderRunning ? 'TRADING' : 'Offline'}
                      </div>
                      <button
                        onClick={autoTraderRunning ? stopAutoTrader : startAutoTrader}
                        className={`px-3 py-1 rounded-lg font-bold text-xs transition-all duration-200 ${
                          autoTraderRunning
                            ? 'bg-red-500 hover:bg-red-600 text-white hover:shadow-lg hover:shadow-red-500/30'
                            : 'bg-green-500 hover:bg-green-600 text-white hover:shadow-lg hover:shadow-green-500/30'
                        }`}
                      >
                        {autoTraderRunning ? 'Stop' : 'Execute'}
                      </button>
                    </div>
                  </div>
                </div>
              </BreathingCard>

              <BreathingCard intensity="subtle">
                <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                    <div className="text-sm font-medium text-gray-400 flex items-center gap-2 mb-3">
                      <PulseDot color="purple" size="sm" />
                      {DEGEN_EMOJIS.eyes} Price Scanner
                    </div>
                    <div className="flex items-center justify-between">
                      <div className={`px-3 py-1 rounded-full text-xs font-medium ${priceMonitorRunning ? 'bg-purple-500/20 text-purple-400' : 'bg-gray-500/20 text-gray-400'}`}>
                        {priceMonitorRunning ? `${DEGEN_EMOJIS.eyes} Watching` : `${DEGEN_EMOJIS.chartDown} Blind`}
                      </div>
                      <button
                        onClick={priceMonitorRunning ? stopPriceMonitor : startPriceMonitor}
                        className={`px-3 py-1 rounded-lg font-bold text-xs transition-all duration-200 ${
                          priceMonitorRunning
                            ? 'bg-red-500 hover:bg-red-600 text-white hover:shadow-lg hover:shadow-red-500/30'
                            : 'bg-gray-600 hover:bg-gray-500 text-white hover:shadow-lg'
                        }`}
                      >
                        {priceMonitorRunning ? `${DEGEN_EMOJIS.error} Stop` : `${DEGEN_EMOJIS.eyes} Watch`}
                      </button>
                    </div>
                  </div>
                </div>
              </BreathingCard>

              <BreathingCard intensity="strong">
                <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-4 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300 relative overflow-hidden">
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30 relative z-10">
                    <div className="text-sm font-medium text-gray-400 flex items-center gap-2 mb-3">
                      <PulseDot color="blue" size="sm" />
                      {DEGEN_EMOJIS.money} Available SOL
                    </div>
                    <LiveIndicator
                      value={balance?.sol || 0}
                      type="volume"
                      className="text-2xl font-bold text-white"
                    >
                      {balance ? formatSOL(balance.sol) : 'Loading...'}
                    </LiveIndicator>
                    <p className="text-sm text-gray-400 mt-1 flex items-center gap-1">
                      {DEGEN_EMOJIS.fire} {balance ? formatCurrency(balance.sol * 98.45) : ''} Ready to Trade
                    </p>
                  </div>
                </div>
              </BreathingCard>
            </div>

            {/* Diamond Hands Configuration */}
            <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-6 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300">
              <h2 className="text-xl font-bold text-white flex items-center gap-2 mb-6">
                {DEGEN_EMOJIS.target} Diamond Hands Configuration {DEGEN_EMOJIS.diamond}
              </h2>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30">
                    <label className="block text-sm font-medium text-green-400 mb-2 flex items-center gap-1">
                      {DEGEN_EMOJIS.money} Default Trade Amount (SOL)
                    </label>
                    <Input
                      type="number"
                      step="0.01"
                      defaultValue={autoTraderSettings?.defaultAmount || 0.1}
                      disabled
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30">
                    <label className="block text-sm font-medium text-gray-400 mb-2 flex items-center gap-1">
                      {DEGEN_EMOJIS.chart} Max Bag Size (%)
                    </label>
                    <Input
                      type="number"
                      step="0.1"
                      defaultValue={autoTraderSettings?.maxPositionPercentage || 5}
                      disabled
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30">
                    <label className="block text-sm font-medium text-red-400 mb-2 flex items-center gap-1">
                      {DEGEN_EMOJIS.chartDown} Stop Rekt (%)
                    </label>
                    <Input
                      type="number"
                      step="0.1"
                      defaultValue={autoTraderSettings?.stopLossPercentage || 30}
                      disabled
                      className="bg-gray-700 border-gray-600 text-white"
                    />
                  </div>
                  <div className="bg-gray-800/80 backdrop-blur-sm rounded-lg p-3 border border-gray-700/30">
                    <label className="block text-sm font-medium text-gray-400 mb-2 flex items-center gap-1">
                      {DEGEN_EMOJIS.rocket} Secure Bag Mode
                    </label>
                    <div className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={autoTraderSettings?.profitTakingEnabled || false}
                        disabled
                        className="rounded border-gray-600 text-green-500 focus:ring-green-500"
                      />
                      <span className="text-sm text-white font-semibold">
                        {autoTraderSettings?.profitTakingEnabled ? `${DEGEN_EMOJIS.success} Active` : `${DEGEN_EMOJIS.error} Disabled`}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="pt-4 border-t border-gray-700/50">
                  <p className="text-sm text-gray-400">
                    Diamond hands mode will automatically ape into alpha signals.
                    Configure your degen settings in the Settings page.
                  </p>
                </div>
              </div>
            </div>

            {/* Trade History */}
            <div className="bg-gray-900 border-2 border-gray-700/60 rounded-2xl p-6 hover:border-gray-600 hover:shadow-lg hover:shadow-gray-500/10 transition-all duration-300">
              <h2 className="text-xl font-bold text-white flex items-center gap-2 mb-6">
                {DEGEN_EMOJIS.chart} Recent Trade Activity {DEGEN_EMOJIS.fire}
              </h2>
              <div className="text-center py-8 text-gray-400">
                <div className="text-6xl mb-4 animate-float">
                  {DEGEN_EMOJIS.chart}
                </div>
                <p className="text-white font-semibold">No trades yet, anon {DEGEN_EMOJIS.eyes}</p>
                <p className="text-sm mt-1 text-gray-400">
                  Your diamond hands activity will appear here once you start trading {DEGEN_EMOJIS.rocket}
                </p>
              </div>
            </div>
          </>
        )}

        {/* Manual Trade Modal */}
        <Modal
          isOpen={showTradeModal}
          onClose={() => setShowTradeModal(false)}
          title={`${DEGEN_EMOJIS.target} Manual Trade ${DEGEN_EMOJIS.rocket}`}
          size="md"
        >
          <div className="space-y-4">
            {/* Trade Type */}
            <div>
              <label className="block text-sm font-medium text-white mb-2 flex items-center gap-1">
                {DEGEN_EMOJIS.lightning} Trade Type
              </label>
              <div className="flex space-x-2">
                <button
                  onClick={() => setTradeType('buy')}
                  className={`flex-1 px-4 py-2 rounded-lg font-bold transition-all duration-200 ${
                    tradeType === 'buy'
                      ? 'bg-green-500 text-white hover:bg-green-600 hover:shadow-lg hover:shadow-green-500/30'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600'
                  }`}
                >
                  BUY
                </button>
                <button
                  onClick={() => setTradeType('sell')}
                  className={`flex-1 px-4 py-2 rounded-lg font-bold transition-all duration-200 ${
                    tradeType === 'sell'
                      ? 'bg-green-500 text-white hover:bg-green-600 hover:shadow-lg hover:shadow-green-500/30'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600 border border-gray-600'
                  }`}
                >
                  SELL
                </button>
              </div>
            </div>

            {/* Token Address */}
            <Input
              label={`${DEGEN_EMOJIS.gem} Token Contract Address`}
              placeholder="Enter the gem's contract address"
              value={tokenAddress}
              onChange={(e) => setTokenAddress(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white"
            />

            {/* Token Symbol */}
            <Input
              label={`${DEGEN_EMOJIS.fire} Token Symbol (Optional)`}
              placeholder="e.g., BONK, SAMO, WIF"
              value={tokenSymbol}
              onChange={(e) => setTokenSymbol(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white"
            />

            {/* Trade Amount */}
            <Input
              label={`${DEGEN_EMOJIS.money} Trade Amount (${tradeType === 'buy' ? 'SOL' : 'Tokens'})`}
              type="number"
              step="0.01"
              value={tradeAmount}
              onChange={(e) => setTradeAmount(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white"
            />

            {/* Slippage */}
            <Input
              label={`${DEGEN_EMOJIS.target} Slippage Tolerance (%)`}
              type="number"
              step="0.1"
              value={slippage}
              onChange={(e) => setSlippage(e.target.value)}
              className="bg-gray-700 border-gray-600 text-white"
            />

            {/* Error Display */}
            {tradingError && (
              <div className="bg-red-900/50 border-2 border-red-500/60 rounded-lg p-3">
                <p className="text-sm text-white font-semibold flex items-center gap-1">
                  {DEGEN_EMOJIS.error} Something went rekt: {tradingError}
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                onClick={() => setShowTradeModal(false)}
                className="flex-1 bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-lg font-bold transition-all duration-200"
              >
                Cancel
              </button>
              <button
                onClick={handleTrade}
                disabled={tradingLoading}
                className={`flex-1 px-4 py-2 rounded-lg font-bold transition-all duration-200 ${
                  tradingLoading
                    ? 'bg-gray-600 cursor-not-allowed'
                    : 'bg-green-500 hover:bg-green-600 hover:shadow-lg hover:shadow-green-500/30'
                } text-white`}
              >
                {tradingLoading ? 'Processing...' : (
                  tradeType === 'buy' ? 'BUY NOW' : 'SELL'
                )}
              </button>
            </div>
          </div>
        </Modal>
      </div>
    </MainLayout>
  );
}
