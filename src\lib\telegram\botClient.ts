// Telegram Bot Client - Handles Telegram Bot API interactions

import { TELEGRAM_CONFIG } from '../constants';
import { SignalParser } from './signalParser';
import { SignalService } from '../supabase/services/signalService';
import type { TelegramSignal, ParsedSignal } from '@/types';

interface TelegramMessage {
  message_id: number;
  from?: {
    id: number;
    is_bot: boolean;
    first_name: string;
    username?: string;
  };
  chat: {
    id: number;
    title?: string;
    username?: string;
    type: string;
  };
  date: number;
  text?: string;
  caption?: string;
  photo?: Array<{
    file_id: string;
    file_unique_id: string;
    width: number;
    height: number;
    file_size?: number;
  }>;
}

interface TelegramUpdate {
  update_id: number;
  message?: TelegramMessage;
  channel_post?: TelegramMessage;
}

export class TelegramBotClient {
  private botToken: string;
  private webhookUrl?: string;
  private isRunning = false;

  constructor(botToken?: string, webhookUrl?: string) {
    this.botToken = botToken || TELEGRAM_CONFIG.botToken || '';
    this.webhookUrl = webhookUrl || TELEGRAM_CONFIG.webhookUrl;
  }

  /**
   * Set up webhook for receiving updates
   */
  async setupWebhook(): Promise<boolean> {
    if (!this.botToken || !this.webhookUrl) {
      console.error('Bot token or webhook URL not configured');
      return false;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${this.botToken}/setWebhook`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          url: this.webhookUrl,
          allowed_updates: ['message', 'channel_post'],
        }),
      });

      const result = await response.json();
      
      if (result.ok) {
        console.log('Webhook set up successfully');
        return true;
      } else {
        console.error('Failed to set up webhook:', result.description);
        return false;
      }
    } catch (error) {
      console.error('Error setting up webhook:', error);
      return false;
    }
  }

  /**
   * Remove webhook
   */
  async removeWebhook(): Promise<boolean> {
    if (!this.botToken) {
      console.error('Bot token not configured');
      return false;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${this.botToken}/deleteWebhook`, {
        method: 'POST',
      });

      const result = await response.json();
      
      if (result.ok) {
        console.log('Webhook removed successfully');
        return true;
      } else {
        console.error('Failed to remove webhook:', result.description);
        return false;
      }
    } catch (error) {
      console.error('Error removing webhook:', error);
      return false;
    }
  }

  /**
   * Process incoming webhook update
   */
  async processUpdate(update: TelegramUpdate): Promise<void> {
    try {
      const message = update.message || update.channel_post;
      if (!message) {
        return;
      }

      await this.processMessage(message);
    } catch (error) {
      console.error('Error processing update:', error);
    }
  }

  /**
   * Process a Telegram message
   */
  private async processMessage(message: TelegramMessage): Promise<void> {
    try {
      const content = message.text || message.caption || '';
      if (!content) {
        return;
      }

      const channelId = message.chat.id.toString();
      const channelName = message.chat.title || message.chat.username || 'Unknown';
      const messageId = message.message_id;
      const timestamp = new Date(message.date * 1000);

      // Extract image URLs if present
      const images: string[] = [];
      if (message.photo && message.photo.length > 0) {
        // Get the largest photo
        const largestPhoto = message.photo.reduce((prev, current) => 
          (prev.file_size || 0) > (current.file_size || 0) ? prev : current
        );
        
        const fileUrl = await this.getFileUrl(largestPhoto.file_id);
        if (fileUrl) {
          images.push(fileUrl);
        }
      }

      // Parse the message for signals
      const parsedSignal = SignalParser.parseMessage(
        content,
        channelId,
        messageId,
        timestamp,
        images
      );

      // Create telegram signal record
      const telegramSignal: Omit<TelegramSignal, 'id' | 'createdAt'> = {
        channelId,
        channelName,
        messageId,
        content,
        tokenAddress: parsedSignal?.tokenAddress,
        tokenSymbol: parsedSignal?.tokenSymbol,
        marketCap: parsedSignal?.marketCap,
        images,
        timestamp,
        processed: false,
        valid: false,
        error: null,
      };

      // Save to database
      const savedSignal = await SignalService.recordSignal(telegramSignal);
      
      if (savedSignal && parsedSignal) {
        // Validate the signal
        const validation = SignalParser.validateSignal(parsedSignal);
        
        // Update signal status
        await SignalService.updateSignalStatus(
          savedSignal.id,
          true, // processed
          validation.valid,
          validation.errors.length > 0 ? validation.errors.join(', ') : undefined
        );

        if (validation.valid) {
          console.log(`Valid signal detected: ${parsedSignal.tokenSymbol || parsedSignal.tokenAddress}`);
          
          // Trigger trading logic for valid signals
          await this.handleValidSignal(parsedSignal, savedSignal);
        } else {
          console.log(`Invalid signal: ${validation.errors.join(', ')}`);
        }
      }
    } catch (error) {
      console.error('Error processing message:', error);
    }
  }

  /**
   * Handle a valid trading signal
   */
  private async handleValidSignal(signal: ParsedSignal, telegramSignal: TelegramSignal): Promise<void> {
    try {
      // Process valid trading signal
      console.log('Processing valid signal:', {
        token: signal.tokenSymbol || signal.tokenAddress,
        confidence: signal.confidence,
        trigger: signal.triggerPhrase,
        channel: telegramSignal.channelName,
      });

      // Signal processing workflow:
      // 1. Fetch token metadata and current price
      // 2. Check if auto-trading is enabled for user
      // 3. Validate trading conditions (balance, risk limits, etc.)
      // 4. Execute trade if conditions are met and auto-trading enabled
      // 5. Record trade execution in database
      // 6. Send notifications to users about signal and trade status
    } catch (error) {
      console.error('Error handling valid signal:', error);
    }
  }

  /**
   * Get file URL from Telegram
   */
  private async getFileUrl(fileId: string): Promise<string | null> {
    if (!this.botToken) {
      return null;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${this.botToken}/getFile?file_id=${fileId}`);
      const result = await response.json();
      
      if (result.ok && result.result.file_path) {
        return `https://api.telegram.org/file/bot${this.botToken}/${result.result.file_path}`;
      }
    } catch (error) {
      console.error('Error getting file URL:', error);
    }
    
    return null;
  }

  /**
   * Send message to a chat
   */
  async sendMessage(chatId: string | number, text: string): Promise<boolean> {
    if (!this.botToken) {
      console.error('Bot token not configured');
      return false;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${this.botToken}/sendMessage`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          chat_id: chatId,
          text,
          parse_mode: 'HTML',
        }),
      });

      const result = await response.json();
      return result.ok;
    } catch (error) {
      console.error('Error sending message:', error);
      return false;
    }
  }

  /**
   * Get bot information
   */
  async getBotInfo(): Promise<any> {
    if (!this.botToken) {
      console.error('Bot token not configured');
      return null;
    }

    try {
      const response = await fetch(`https://api.telegram.org/bot${this.botToken}/getMe`);
      const result = await response.json();
      
      if (result.ok) {
        return result.result;
      } else {
        console.error('Failed to get bot info:', result.description);
        return null;
      }
    } catch (error) {
      console.error('Error getting bot info:', error);
      return null;
    }
  }

  /**
   * Start long polling (for development/testing)
   */
  async startPolling(): Promise<void> {
    if (!this.botToken) {
      console.error('Bot token not configured');
      return;
    }

    this.isRunning = true;
    let offset = 0;

    console.log('Starting Telegram bot polling...');

    while (this.isRunning) {
      try {
        const response = await fetch(`https://api.telegram.org/bot${this.botToken}/getUpdates?offset=${offset}&timeout=30`);
        const result = await response.json();

        if (result.ok && result.result.length > 0) {
          for (const update of result.result) {
            await this.processUpdate(update);
            offset = update.update_id + 1;
          }
        }
      } catch (error) {
        console.error('Error in polling:', error);
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, 5000));
      }
    }
  }

  /**
   * Stop polling
   */
  stopPolling(): void {
    this.isRunning = false;
    console.log('Stopping Telegram bot polling...');
  }
}
