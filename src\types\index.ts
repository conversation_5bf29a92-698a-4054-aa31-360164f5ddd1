// Signal V1 - Type Definitions

import { PublicKey } from '@solana/web3.js';

// ============================================================================
// User & Authentication Types
// ============================================================================

export interface User {
  id: string;
  email?: string;
  walletAddress?: string;
  createdAt: Date;
  updatedAt: Date;
  preferences: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  notifications: NotificationSettings;
  trading: TradingSettings;
  display: DisplaySettings;
}

export interface NotificationSettings {
  signals: boolean;
  trades: boolean;
  priceAlerts: boolean;
  portfolio: boolean;
  push: boolean;
  email: boolean;
}

export interface DisplaySettings {
  currency: 'USD' | 'SOL';
  language: string;
  timezone: string;
  compactMode: boolean;
}

// ============================================================================
// Trading & Portfolio Types
// ============================================================================

export interface TradingSettings {
  defaultAmount: number;
  maxPositionPercentage: number;
  stopLossPercentage: number;
  slippageTolerance: number;
  autoTrade: boolean;
  maxActivePositions: number;
  profitTakingEnabled: boolean;
}

export interface Portfolio {
  id: string;
  userId: string;
  totalValue: number;
  totalPnL: number;
  totalPnLPercentage: number;
  positions: Position[];
  trades: Trade[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Position {
  id: string;
  portfolioId: string;
  tokenAddress: string;
  tokenSymbol: string;
  tokenName: string;
  amount: number;
  entryPrice: number;
  currentPrice: number;
  entryValue: number;
  currentValue: number;
  pnl: number;
  pnlPercentage: number;
  multiplier: number;
  status: 'active' | 'closed';
  entryDate: Date;
  exitDate?: Date;
  signalId?: string;
}

export interface Trade {
  id: string;
  portfolioId: string;
  positionId?: string;
  type: 'buy' | 'sell';
  tokenAddress: string;
  tokenSymbol: string;
  amount: number;
  price: number;
  value: number;
  slippage: number;
  fees: number;
  signature: string;
  status: 'pending' | 'confirmed' | 'failed';
  timestamp: Date;
  signalId?: string;
}

// ============================================================================
// Signal & Telegram Types
// ============================================================================

export interface TelegramSignal {
  id: string;
  channelId: string;
  channelName: string;
  messageId: number;
  content: string;
  tokenAddress?: string;
  tokenSymbol?: string;
  marketCap?: number;
  images?: string[];
  timestamp: Date;
  processed: boolean;
  valid: boolean;
  error?: string;
}

export interface ParsedSignal {
  tokenAddress: string;
  tokenSymbol?: string;
  marketCap?: number;
  triggerPhrase: string;
  confidence: number;
  metadata: {
    channelId: string;
    messageId: number;
    timestamp: Date;
    images?: string[];
  };
}

export interface TelegramChannel {
  id: string;
  name: string;
  username?: string;
  description?: string;
  memberCount?: number;
  active: boolean;
  lastSignal?: Date;
  signalCount: number;
}

// ============================================================================
// Token & Market Data Types
// ============================================================================

export interface Token {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
  verified?: boolean;
}

export interface TokenPrice {
  address: string;
  price: number;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
  liquidity: number;
  holders?: number;
  ath?: number;
  athDate?: Date;
  timestamp: Date;
}

export interface TokenMetrics {
  address: string;
  symbol: string;
  name: string;
  price: number;
  priceChange24h: number;
  volume24h: number;
  marketCap: number;
  liquidity: number;
  holders: number;
  ath: number;
  athDate: Date;
  multiplierFromSignal?: number;
  signalDate?: Date;
  riskScore: number;
  tags: string[];
}

// ============================================================================
// Jupiter & DEX Types
// ============================================================================

export interface JupiterQuote {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  platformFee?: {
    amount: string;
    feeBps: number;
  };
  priceImpactPct: string;
  routePlan: RoutePlan[];
}

export interface RoutePlan {
  swapInfo: {
    ammKey: string;
    label: string;
    inputMint: string;
    outputMint: string;
    inAmount: string;
    outAmount: string;
    feeAmount: string;
    feeMint: string;
  };
  percent: number;
}

export interface SwapTransaction {
  swapTransaction: string;
  lastValidBlockHeight: number;
}

// ============================================================================
// API Response Types
// ============================================================================

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: Date;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// ============================================================================
// Wallet Types
// ============================================================================

export interface WalletInfo {
  publicKey: PublicKey;
  connected: boolean;
  connecting: boolean;
  disconnecting: boolean;
  wallet: any;
}

export interface WalletBalance {
  sol: number;
  tokens: TokenBalance[];
  totalValue: number;
}

export interface TokenBalance {
  mint: string;
  symbol: string;
  name: string;
  amount: number;
  decimals: number;
  value: number;
  logoURI?: string;
}

// ============================================================================
// UI & Component Types
// ============================================================================

export interface ChartData {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface AlertConfig {
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
}

// ============================================================================
// Error Types
// ============================================================================

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

// ============================================================================
// Configuration Types
// ============================================================================

export interface AppConfig {
  solana: {
    network: string;
    rpcUrl: string;
    commitment: string;
  };
  jupiter: {
    apiUrl: string;
    slippageBps: number;
  };
  telegram: {
    // User Account Authentication
    apiId?: string;
    apiHash?: string;
    sessionString?: string;
    phoneNumber?: string;
    // Legacy bot config (deprecated)
    botToken?: string;
    webhookUrl?: string;
    signalTriggers: string[];
  };
  trading: {
    defaultAmount: number;
    maxPositionPercentage: number;
    stopLossPercentage: number;
    maxActivePositions: number;
  };
}
