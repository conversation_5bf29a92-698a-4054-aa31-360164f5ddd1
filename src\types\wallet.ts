// Wallet-specific type definitions

declare global {
  interface Window {
    solana?: {
      isPhantom?: boolean;
      connect?: () => Promise<{ publicKey: { toString(): string } }>;
      disconnect?: () => Promise<void>;
      signTransaction?: (transaction: any) => Promise<any>;
      signAllTransactions?: (transactions: any[]) => Promise<any[]>;
      signMessage?: (message: Uint8Array) => Promise<{ signature: Uint8Array }>;
    };
    solflare?: {
      isSolflare?: boolean;
      connect?: () => Promise<{ publicKey: { toString(): string } }>;
      disconnect?: () => Promise<void>;
      signTransaction?: (transaction: any) => Promise<any>;
      signAllTransactions?: (transactions: any[]) => Promise<any[]>;
      signMessage?: (message: Uint8Array) => Promise<{ signature: Uint8Array }>;
    };
    backpack?: {
      isBackpack?: boolean;
      connect?: () => Promise<{ publicKey: { toString(): string } }>;
      disconnect?: () => Promise<void>;
      signTransaction?: (transaction: any) => Promise<any>;
      signAllTransactions?: (transactions: any[]) => Promise<any[]>;
      signMessage?: (message: Uint8Array) => Promise<{ signature: Uint8Array }>;
    };
    glow?: {
      connect?: () => Promise<{ publicKey: { toString(): string } }>;
      disconnect?: () => Promise<void>;
      signTransaction?: (transaction: any) => Promise<any>;
      signAllTransactions?: (transactions: any[]) => Promise<any[]>;
      signMessage?: (message: Uint8Array) => Promise<{ signature: Uint8Array }>;
    };
  }
}

export interface WalletError extends Error {
  name: 'WalletNotReadyError' | 'WalletConnectionError' | 'WalletAccountError' | 'WalletSignTransactionError';
}

export interface WalletReadinessCheck {
  phantom: boolean;
  solflare: boolean;
  backpack: boolean;
  glow: boolean;
}

export interface WalletConnectionState {
  connected: boolean;
  connecting: boolean;
  disconnecting: boolean;
  publicKey: string | null;
  error: string | null;
}

export {};
