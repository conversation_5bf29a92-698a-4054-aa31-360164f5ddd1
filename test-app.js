// Signal V1 - Application Testing Script
// Run this script to test basic application functionality

const https = require('https');
const http = require('http');

const BASE_URL = 'http://localhost:3000';

// Test endpoints
const endpoints = [
  '/',
  '/portfolio', 
  '/signals',
  '/trading',
  '/analytics',
  '/settings'
];

console.log('🚀 Signal V1 - Application Testing Started\n');

async function testEndpoint(path) {
  return new Promise((resolve) => {
    const url = `${BASE_URL}${path}`;
    
    http.get(url, (res) => {
      const status = res.statusCode;
      const success = status === 200;
      
      console.log(`${success ? '✅' : '❌'} ${path.padEnd(12)} - Status: ${status}`);
      
      resolve({ path, status, success });
    }).on('error', (err) => {
      console.log(`❌ ${path.padEnd(12)} - Error: ${err.message}`);
      resolve({ path, status: 0, success: false, error: err.message });
    });
  });
}

async function runTests() {
  console.log('Testing application endpoints...\n');
  
  const results = [];
  
  for (const endpoint of endpoints) {
    const result = await testEndpoint(endpoint);
    results.push(result);
    
    // Small delay between requests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  const successful = results.filter(r => r.success).length;
  const total = results.length;
  
  console.log(`✅ Successful: ${successful}/${total}`);
  console.log(`❌ Failed: ${total - successful}/${total}`);
  
  if (successful === total) {
    console.log('\n🎉 All tests passed! Application is running correctly.');
    console.log('\n📋 Next Steps:');
    console.log('1. Set up Supabase database (see SETUP_GUIDE.md)');
    console.log('2. Update .env.local with real Supabase credentials');
    console.log('3. Test wallet connection in browser');
    console.log('4. Verify all components load without errors');
  } else {
    console.log('\n⚠️  Some tests failed. Check the application logs.');
  }
  
  console.log('\n🌐 Application URL: http://localhost:3000');
  console.log('📖 Setup Guide: ./SETUP_GUIDE.md');
}

// Run the tests
runTests().catch(console.error);
