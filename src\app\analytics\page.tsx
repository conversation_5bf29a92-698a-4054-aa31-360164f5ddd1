'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { LiveIndicator, PulseDot, BreathingCard, ShimmerText } from '@/components/ui/LiveIndicator';
import { PerformanceChart, TokenAllocationChart, TradeVolumeChart } from '@/components/analytics/PerformanceChart';
import { useWallet } from '@/hooks/useWallet';
import { useStaggeredEntrance } from '@/hooks/useAnimations';
import { formatCurrency, formatPercentage, getChangeColor } from '@/lib/utils';
import { cn } from '@/lib/utils';
import {
  getDegenTerm,
  formatDegenAmount,
  formatDegenPercentage,
  getDegenMultiplier,
  DEGEN_EMOJIS
} from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';

export default function AnalyticsPage() {
  const { connected } = useWallet();
  const [timeframe, setTimeframe] = useState<'24h' | '7d' | '30d' | '90d' | 'all'>('7d');
  const { containerRef, isVisible } = useStaggeredEntrance(6, 150);

  // Mock analytics data
  const performanceData = [
    { date: '2024-01-01', portfolioValue: 100, pnl: 0, pnlPercentage: 0 },
    { date: '2024-01-02', portfolioValue: 105, pnl: 5, pnlPercentage: 5 },
    { date: '2024-01-03', portfolioValue: 98, pnl: -2, pnlPercentage: -2 },
    { date: '2024-01-04', portfolioValue: 115, pnl: 15, pnlPercentage: 15 },
    { date: '2024-01-05', portfolioValue: 125, pnl: 25, pnlPercentage: 25 },
    { date: '2024-01-06', portfolioValue: 140, pnl: 40, pnlPercentage: 40 },
    { date: '2024-01-07', portfolioValue: 155, pnl: 55, pnlPercentage: 55 },
  ];

  const allocationData = [
    { name: 'BONK', value: 85, percentage: 54.8, color: '#10B981' },
    { name: 'ORCA', value: 63, percentage: 40.6, color: '#3B82F6' },
    { name: 'SAMO', value: 9, percentage: 5.8, color: '#F59E0B' },
  ];

  const volumeData = [
    { date: '2024-01-01', buyVolume: 25, sellVolume: 0 },
    { date: '2024-01-02', buyVolume: 10, sellVolume: 5 },
    { date: '2024-01-03', buyVolume: 51.75, sellVolume: 0 },
    { date: '2024-01-04', buyVolume: 0, sellVolume: 12 },
    { date: '2024-01-05', buyVolume: 15, sellVolume: 8 },
    { date: '2024-01-06', buyVolume: 0, sellVolume: 0 },
    { date: '2024-01-07', buyVolume: 20, sellVolume: 15 },
  ];

  const stats = {
    totalTrades: 12,
    winRate: 75,
    avgHoldTime: '2.3 days',
    bestTrade: 240,
    worstTrade: -10,
    totalFees: 2.45,
    sharpeRatio: 1.85,
  };

  const timeframes = [
    { key: '24h', label: '24H' },
    { key: '7d', label: '7D' },
    { key: '30d', label: '30D' },
    { key: '90d', label: '90D' },
    { key: 'all', label: 'All' },
  ] as const;

  return (
    <MainLayout>
      <div className="space-y-6" ref={containerRef}>
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between slide-up-enter">
          <div>
            <ShimmerText speed="slow">
              <h1 className="text-4xl font-bold text-primary">
                Big Brain Stats
              </h1>
            </ShimmerText>
            <p className="mt-3 text-lg text-degen-purple font-medium fade-in-enter">
              Deep dive into your degen performance and alpha insights
            </p>
            <div className="flex items-center gap-2 mt-2">
              <PulseDot color="purple" size="sm" />
              <PulseDot color="blue" size="sm" />
              <PulseDot color="green" size="sm" />
            </div>
          </div>

          <div className="mt-4 sm:mt-0 flex items-center space-x-2">
            {timeframes.map((tf) => (
              <Button
                key={tf.key}
                size="sm"
                variant={timeframe === tf.key ? 'diamond' : 'outline'}
                onClick={() => setTimeframe(tf.key)}
                className="font-bold transition-colors-smooth"
              >
                {tf.label}
              </Button>
            ))}
          </div>
        </div>

        {!connected ? (
          <Card className="bg-gradient-loss border-2 border-degen-red/50 glow-red">
            <CardContent className="p-8 text-center">
              <div className="text-6xl mb-4 animate-bounce">
                {DEGEN_EMOJIS.brain}
              </div>
              <h3 className="text-xl font-bold text-white mb-2 flex items-center justify-center gap-2">
                {DEGEN_EMOJIS.chartDown} No Brain Data Available {DEGEN_EMOJIS.chartDown}
              </h3>
              <p className="text-white">
                Connect your wallet to unlock big brain analytics {DEGEN_EMOJIS.brain} Can't analyze gains without connection {DEGEN_EMOJIS.eyes}
              </p>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Big Brain Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
              <Card className="bg-gradient-degen border-degen-blue/30 hover:glow-blue transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-blue mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.target} Total Sends
                    </p>
                    <p className="text-lg font-bold text-degen-blue">{stats.totalTrades}</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-degen border-degen-green/30 hover:glow-green transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-green mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.rocket} Moon Rate
                    </p>
                    <p className="text-lg font-bold text-degen-green">
                      {stats.winRate}% {DEGEN_EMOJIS.chart}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-degen border-degen-purple/30 hover:glow-purple transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-purple mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.diamond} Avg Diamond Time
                    </p>
                    <p className="text-lg font-bold text-degen-purple">{stats.avgHoldTime}</p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-degen border-degen-gold/30 hover:glow-gold transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-gold mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.moon} Best Moon
                    </p>
                    <p className="text-lg font-bold text-degen-gold">
                      +{stats.bestTrade}% {DEGEN_EMOJIS.rocket}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-degen border-degen-red/30 hover:glow-red transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-red mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.chartDown} Worst Rekt
                    </p>
                    <p className="text-lg font-bold text-degen-red">
                      {stats.worstTrade}% {DEGEN_EMOJIS.chartDown}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-degen border-degen-gray/30 hover:glow-blue transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-gray mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.fire} Total Fees
                    </p>
                    <p className="text-lg font-bold text-degen-blue">
                      {formatCurrency(stats.totalFees)}
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-gradient-degen border-degen-blue/30 hover:glow-blue transition-all duration-300">
                <CardContent className="p-4">
                  <div className="text-center">
                    <p className="text-xs text-degen-blue mb-1 flex items-center justify-center gap-1">
                      {DEGEN_EMOJIS.brain} Big Brain Score
                    </p>
                    <p className="text-lg font-bold text-degen-blue">
                      {stats.sharpeRatio} {DEGEN_EMOJIS.brain}
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <PerformanceChart data={performanceData} timeframe={timeframe} />
              <TokenAllocationChart data={allocationData} />
            </div>

            <TradeVolumeChart data={volumeData} />

            {/* Degen Send Analysis */}
            <Card className="bg-gradient-degen border-degen-gold/30">
              <CardHeader>
                <CardTitle className="text-degen-gold flex items-center gap-2">
                  {DEGEN_EMOJIS.brain} Big Brain Send Analysis {DEGEN_EMOJIS.chart}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {/* Top Moon Missions */}
                  <div>
                    <h4 className="text-sm font-medium text-degen-green mb-3 flex items-center gap-2">
                      {DEGEN_EMOJIS.rocket} Top Moon Missions
                    </h4>
                    <div className="space-y-2">
                      {[
                        { token: 'BONK', pnl: 240, value: 60, date: '2 days ago' },
                        { token: 'ORCA', pnl: 21.7, value: 11.25, date: '3 days ago' },
                      ].map((trade, index) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-degen-charcoal border border-degen-green/30 rounded-lg hover:glow-green transition-all duration-300">
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-profit rounded-full flex items-center justify-center">
                              <span className="text-xs font-bold text-white">
                                {trade.token.slice(0, 2)}
                              </span>
                            </div>
                            <div>
                              <p className="font-medium text-degen-gold flex items-center gap-1">
                                {DEGEN_EMOJIS.gem} {trade.token}
                              </p>
                              <p className="text-xs text-degen-gray">{trade.date}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-bold text-degen-green flex items-center gap-1">
                              {DEGEN_EMOJIS.rocket} +{formatPercentage(trade.pnl)}
                            </p>
                            <p className="text-xs text-degen-blue">
                              {formatCurrency(trade.value)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Alpha Signal Performance */}
                  <div>
                    <h4 className="text-sm font-medium text-degen-purple mb-3 flex items-center gap-2">
                      {DEGEN_EMOJIS.lightning} Alpha Signal Performance
                    </h4>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="text-center p-4 bg-degen-charcoal border border-degen-blue/30 rounded-lg hover:glow-blue transition-all duration-300">
                        <p className="text-2xl font-bold text-degen-blue">{DEGEN_EMOJIS.target} 3</p>
                        <p className="text-sm text-degen-gray">Alpha Signals Sent</p>
                      </div>
                      <div className="text-center p-4 bg-degen-charcoal border border-degen-green/30 rounded-lg hover:glow-green transition-all duration-300">
                        <p className="text-2xl font-bold text-degen-green">{DEGEN_EMOJIS.rocket} 2</p>
                        <p className="text-sm text-degen-gray">Moon Missions</p>
                      </div>
                      <div className="text-center p-4 bg-degen-charcoal border border-degen-purple/30 rounded-lg hover:glow-purple transition-all duration-300">
                        <p className="text-2xl font-bold text-degen-purple">{DEGEN_EMOJIS.brain} 67%</p>
                        <p className="text-sm text-degen-gray">Big Brain Rate</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </MainLayout>
  );
}
