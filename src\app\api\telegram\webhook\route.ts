// Telegram Webhook API Route

import { NextRequest, NextResponse } from 'next/server';
import { TelegramBotClient } from '@/lib/telegram/botClient';


// Initialize bot client
const botClient = new TelegramBotClient();

export async function POST(request: NextRequest) {
  try {
    // Verify the request is from Telegram (optional but recommended)
    // const secretToken = request.headers.get('x-telegram-bot-api-secret-token');
    
    // Parse the update
    const update = await request.json();
    
    // Validate update structure
    if (!update || typeof update.update_id !== 'number') {
      return NextResponse.json(
        { error: 'Invalid update format' },
        { status: 400 }
      );
    }

    // Process the update
    await botClient.processUpdate(update);

    // Return success response
    return NextResponse.json({ ok: true });
  } catch (error) {
    console.error('Webhook error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function GET() {
  // Health check endpoint
  return NextResponse.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    webhook: 'active',
  });
}
