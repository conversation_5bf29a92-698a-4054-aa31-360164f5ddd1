'use client';

import { useEffect, useState, useCallback } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase/client';
import { UserService } from '@/lib/supabase/services/userService';
import { PortfolioService } from '@/lib/supabase/services/portfolioService';
import { SignalService } from '@/lib/supabase/services/signalService';
import type { User, Portfolio, TelegramSignal, Trade } from '@/types';

export function useSupabaseAuth() {
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const [userProfile, setUserProfile] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      try {
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Error getting session:', error);
          setError(error.message);
        } else {
          setUser(session?.user ?? null);
          
          if (session?.user) {
            const profile = await UserService.getCurrentUserProfile();
            setUserProfile(profile);
          }
        }
      } catch (err) {
        console.error('Error in getInitialSession:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    getInitialSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user ?? null);
        
        if (session?.user) {
          const profile = await UserService.getCurrentUserProfile();
          setUserProfile(profile);
        } else {
          setUserProfile(null);
        }
        
        setLoading(false);
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const signInWithEmail = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      
      if (error) {
        setError(error.message);
        return false;
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signUpWithEmail = async (email: string, password: string) => {
    try {
      setLoading(true);
      setError(null);
      
      const { error } = await supabase.auth.signUp({
        email,
        password,
      });
      
      if (error) {
        setError(error.message);
        return false;
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const { error } = await supabase.auth.signOut();
      
      if (error) {
        setError(error.message);
        return false;
      }
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      return false;
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    userProfile,
    loading,
    error,
    signInWithEmail,
    signUpWithEmail,
    signOut,
    isAuthenticated: !!user,
  };
}

export function usePortfolio() {
  const [portfolio, setPortfolio] = useState<Portfolio | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPortfolio = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const portfolioData = await PortfolioService.getUserPortfolio();
      setPortfolio(portfolioData);
    } catch (err) {
      console.error('Error fetching portfolio:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchPortfolio();
  }, [fetchPortfolio]);

  return {
    portfolio,
    loading,
    error,
    refetch: fetchPortfolio,
  };
}

export function useSignals(limit: number = 50) {
  const [signals, setSignals] = useState<TelegramSignal[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSignals = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const signalsData = await SignalService.getRecentSignals(limit);
      setSignals(signalsData);
    } catch (err) {
      console.error('Error fetching signals:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch signals');
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchSignals();

    // Subscribe to new signals
    const subscription = SignalService.subscribeToNewSignals((newSignal) => {
      setSignals(prev => [newSignal, ...prev.slice(0, limit - 1)]);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [limit, fetchSignals]);

  return {
    signals,
    loading,
    error,
    refetch: fetchSignals,
  };
}

export interface TradingHistoryItem {
  id: string;
  type: 'buy' | 'sell';
  tokenSymbol: string;
  tokenAddress: string;
  amount: number;
  price: number;
  value: number;
  pnl: number;
  pnlPercentage: number;
  timestamp: Date;
  status: string;
  signature: string;
}

export function useTradingHistory(limit: number = 50) {
  const [data, setData] = useState<TradingHistoryItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTradingHistory = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const trades = await PortfolioService.getRecentTrades(limit);

      // Transform trades to match the expected format for the history page
      const transformedTrades: TradingHistoryItem[] = trades.map(trade => {
        // For now, set PnL to 0 since we need current prices to calculate it
        // This could be enhanced to fetch current prices and calculate real PnL
        const pnl = 0;
        const pnlPercentage = 0;

        return {
          id: trade.id,
          type: trade.type as 'buy' | 'sell',
          tokenSymbol: trade.tokenSymbol,
          tokenAddress: trade.tokenAddress,
          amount: trade.amount,
          price: trade.price,
          value: trade.value,
          pnl,
          pnlPercentage,
          timestamp: trade.timestamp,
          status: trade.status,
          signature: trade.signature,
        };
      });

      setData(transformedTrades);
    } catch (err) {
      console.error('Error fetching trading history:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch trading history');
      // Set empty array on error to prevent crashes
      setData([]);
    } finally {
      setLoading(false);
    }
  }, [limit]);

  useEffect(() => {
    fetchTradingHistory();
  }, [fetchTradingHistory]);

  return {
    data,
    loading,
    error,
    refetch: fetchTradingHistory,
  };
}

interface RealtimePayload<T> {
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: string[] | null;
}

export function useRealtimeSubscription<T>(
  table: string,
  filter?: string,
  callback?: (payload: RealtimePayload<T>) => void
) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const channel = supabase
      .channel(`realtime-${table}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table,
          filter,
        },
        (payload) => {
          if (callback) {
            callback(payload);
          }
          
          // Handle different event types
          if (payload.eventType === 'INSERT') {
            setData(prev => [...prev, payload.new as T]);
          } else if (payload.eventType === 'UPDATE') {
            setData(prev =>
              prev.map(item =>
                (item as T & { id: string }).id === (payload.new as T & { id: string }).id ? payload.new as T : item
              )
            );
          } else if (payload.eventType === 'DELETE') {
            setData(prev =>
              prev.filter(item => (item as T & { id: string }).id !== (payload.old as T & { id: string }).id)
            );
          }
        }
      )
      .subscribe();

    setLoading(false);

    return () => {
      channel.unsubscribe();
    };
  }, [table, filter, callback]);

  return { data, loading };
}
