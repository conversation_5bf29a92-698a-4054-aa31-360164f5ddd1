// Jupiter API Integration - Price data and token information

import { JUPITER_CONFIG, API_ENDPOINTS } from '../constants';
import type { TokenPrice, Token } from '@/types';

// Jupiter API response interfaces
interface JupiterTokenResponse {
  address: string;
  symbol: string;
  name: string;
  decimals: number;
  logoURI?: string;
  tags?: string[];
  verified?: boolean;
}

interface JupiterQuoteResponse {
  inputMint: string;
  inAmount: string;
  outputMint: string;
  outAmount: string;
  otherAmountThreshold: string;
  swapMode: string;
  slippageBps: number;
  platformFee?: {
    amount: string;
    feeBps: number;
  };
  priceImpactPct: string;
  routePlan: Array<{
    swapInfo: {
      ammKey: string;
      label: string;
      inputMint: string;
      outputMint: string;
      inAmount: string;
      outAmount: string;
      feeAmount: string;
      feeMint: string;
    };
    percent: number;
  }>;
}

export class JupiterAPI {
  private static baseUrl = JUPITER_CONFIG.apiUrl;

  /**
   * Get token price from Jupiter using quote endpoint
   * Jupiter doesn't have a direct price endpoint, so we use a small quote to get price
   */
  static async getTokenPrice(tokenAddress: string): Promise<number | null> {
    try {
      // Use USDC as the output token to get USD price
      const USDC_MINT = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v';
      const SOL_MINT = 'So11111111111111111111111111111111111111112';

      // Try to get price by quoting a small amount (1 token unit)
      const amount = 1000000; // 1 token with 6 decimals

      const params = new URLSearchParams({
        inputMint: tokenAddress,
        outputMint: USDC_MINT,
        amount: amount.toString(),
        slippageBps: '50', // 0.5% slippage for price discovery
      });

      const response = await fetch(`${this.baseUrl}/quote?${params}`);

      if (!response.ok) {
        // If direct USDC quote fails, try SOL quote
        const solParams = new URLSearchParams({
          inputMint: tokenAddress,
          outputMint: SOL_MINT,
          amount: amount.toString(),
          slippageBps: '50',
        });

        const solResponse = await fetch(`${this.baseUrl}/quote?${solParams}`);
        if (!solResponse.ok) {
          return null;
        }

        const solData = await solResponse.json();
        if (solData.outAmount) {
          // Convert SOL amount to USD (approximate)
          const solAmount = parseFloat(solData.outAmount) / 1e9; // SOL has 9 decimals
          const solPriceUSD = 100; // Approximate SOL price - should be fetched from another source
          return (solAmount * solPriceUSD) / (amount / 1e6);
        }
        return null;
      }

      const data = await response.json();

      if (data.outAmount) {
        // Calculate price: outAmount (USDC) / inAmount (token)
        const usdcAmount = parseFloat(data.outAmount) / 1e6; // USDC has 6 decimals
        const tokenAmount = amount / 1e6; // Assuming 6 decimals for input token
        return usdcAmount / tokenAmount;
      }

      return null;
    } catch (error) {
      console.error('Error fetching token price from Jupiter:', error);
      return null;
    }
  }

  /**
   * Get multiple token prices
   * Since Jupiter doesn't have batch price endpoint, we'll fetch individually
   */
  static async getTokenPrices(tokenAddresses: string[]): Promise<Record<string, number>> {
    try {
      const prices: Record<string, number> = {};

      // Fetch prices concurrently but limit to avoid rate limiting
      const batchSize = 5;
      for (let i = 0; i < tokenAddresses.length; i += batchSize) {
        const batch = tokenAddresses.slice(i, i + batchSize);
        const batchPromises = batch.map(async (address) => {
          const price = await this.getTokenPrice(address);
          return { address, price };
        });

        const batchResults = await Promise.allSettled(batchPromises);

        batchResults.forEach((result) => {
          if (result.status === 'fulfilled' && result.value.price !== null) {
            prices[result.value.address] = result.value.price;
          }
        });

        // Small delay between batches to avoid rate limiting
        if (i + batchSize < tokenAddresses.length) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      return prices;
    } catch (error) {
      console.error('Error fetching token prices:', error);
      return {};
    }
  }

  /**
   * Get token list from Jupiter
   */
  static async getTokenList(): Promise<Token[]> {
    try {
      const response = await fetch(`${this.baseUrl}/tokens`);
      
      if (!response.ok) {
        console.error('Failed to fetch token list:', response.statusText);
        return [];
      }

      const tokens: JupiterTokenResponse[] = await response.json();

      return tokens.map((token: JupiterTokenResponse) => ({
        address: token.address,
        symbol: token.symbol,
        name: token.name,
        decimals: token.decimals,
        logoURI: token.logoURI,
        tags: token.tags || [],
        verified: token.verified || false,
      }));
    } catch (error) {
      console.error('Error fetching token list:', error);
      return [];
    }
  }

  /**
   * Get token metadata
   */
  static async getTokenMetadata(tokenAddress: string): Promise<Token | null> {
    try {
      const tokenList = await this.getTokenList();
      return tokenList.find(token => token.address === tokenAddress) || null;
    } catch (error) {
      console.error('Error fetching token metadata:', error);
      return null;
    }
  }

  /**
   * Get quote for token swap
   */
  static async getQuote(
    inputMint: string,
    outputMint: string,
    amount: number,
    slippageBps: number = JUPITER_CONFIG.slippageBps
  ): Promise<any> {
    try {
      const params = new URLSearchParams({
        inputMint,
        outputMint,
        amount: amount.toString(),
        slippageBps: slippageBps.toString(),
      });

      const response = await fetch(`${this.baseUrl}/quote?${params}`);
      
      if (!response.ok) {
        console.error('Failed to get quote:', response.statusText);
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting quote:', error);
      return null;
    }
  }

  /**
   * Get swap transaction
   */
  static async getSwapTransaction(
    quoteResponse: any,
    userPublicKey: string,
    wrapUnwrapSOL: boolean = true
  ): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/swap`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          quoteResponse,
          userPublicKey,
          wrapAndUnwrapSol: wrapUnwrapSOL,
        }),
      });

      if (!response.ok) {
        console.error('Failed to get swap transaction:', response.statusText);
        return null;
      }

      return await response.json();
    } catch (error) {
      console.error('Error getting swap transaction:', error);
      return null;
    }
  }
}

/**
 * DexScreener API for additional market data
 */
export class DexScreenerAPI {
  private static baseUrl = API_ENDPOINTS.dexScreener;

  /**
   * Get token data from DexScreener
   */
  static async getTokenData(tokenAddress: string): Promise<any> {
    try {
      const response = await fetch(`${this.baseUrl}/dex/tokens/${tokenAddress}`);
      
      if (!response.ok) {
        console.error('Failed to fetch DexScreener data:', response.statusText);
        return null;
      }

      const data = await response.json();
      
      if (data.pairs && data.pairs.length > 0) {
        // Return the pair with highest liquidity
        return data.pairs.reduce((prev: any, current: any) => 
          (prev.liquidity?.usd || 0) > (current.liquidity?.usd || 0) ? prev : current
        );
      }
      
      return null;
    } catch (error) {
      console.error('Error fetching DexScreener data:', error);
      return null;
    }
  }

  /**
   * Search tokens on DexScreener
   */
  static async searchTokens(query: string): Promise<any[]> {
    try {
      const response = await fetch(`${this.baseUrl}/dex/search/?q=${encodeURIComponent(query)}`);
      
      if (!response.ok) {
        console.error('Failed to search tokens:', response.statusText);
        return [];
      }

      const data = await response.json();
      return data.pairs || [];
    } catch (error) {
      console.error('Error searching tokens:', error);
      return [];
    }
  }
}

/**
 * Combined price tracking service
 */
export class PriceTracker {
  private static priceCache = new Map<string, { price: number; timestamp: number }>();
  private static cacheTimeout = 30000; // 30 seconds

  /**
   * Get comprehensive token price data
   */
  static async getTokenPriceData(tokenAddress: string): Promise<TokenPrice | null> {
    try {
      // Check cache first
      const cached = this.priceCache.get(tokenAddress);
      if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
        return {
          address: tokenAddress,
          price: cached.price,
          priceChange24h: 0, // Would need historical data
          volume24h: 0,
          marketCap: 0,
          liquidity: 0,
          timestamp: new Date(cached.timestamp),
        };
      }

      // Try Jupiter first
      let price = await JupiterAPI.getTokenPrice(tokenAddress);
      
      // Fallback to DexScreener if Jupiter fails
      if (!price) {
        const dexData = await DexScreenerAPI.getTokenData(tokenAddress);
        if (dexData && dexData.priceUsd) {
          price = parseFloat(dexData.priceUsd);
        }
      }

      if (!price) {
        return null;
      }

      // Cache the price
      this.priceCache.set(tokenAddress, {
        price,
        timestamp: Date.now(),
      });

      // Get additional data from DexScreener
      const dexData = await DexScreenerAPI.getTokenData(tokenAddress);
      
      return {
        address: tokenAddress,
        price,
        priceChange24h: dexData?.priceChange?.h24 || 0,
        volume24h: dexData?.volume?.h24 || 0,
        marketCap: dexData?.marketCap || 0,
        liquidity: dexData?.liquidity?.usd || 0,
        timestamp: new Date(),
      };
    } catch (error) {
      console.error('Error getting token price data:', error);
      return null;
    }
  }

  /**
   * Get multiple token price data
   */
  static async getMultipleTokenPriceData(tokenAddresses: string[]): Promise<TokenPrice[]> {
    try {
      const promises = tokenAddresses.map(address => this.getTokenPriceData(address));
      const results = await Promise.allSettled(promises);
      
      return results
        .filter((result): result is PromiseFulfilledResult<TokenPrice | null> => 
          result.status === 'fulfilled' && result.value !== null
        )
        .map(result => result.value!);
    } catch (error) {
      console.error('Error getting multiple token price data:', error);
      return [];
    }
  }

  /**
   * Clear price cache
   */
  static clearCache(): void {
    this.priceCache.clear();
  }

  /**
   * Get cache size
   */
  static getCacheSize(): number {
    return this.priceCache.size;
  }
}
