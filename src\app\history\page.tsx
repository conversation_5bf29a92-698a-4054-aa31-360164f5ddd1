'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { ShimmerText, BreathingCard } from '@/components/ui/LiveIndicator';
import { useStaggeredEntrance } from '@/hooks/useAnimations';
import { useTradingHistory } from '@/hooks/useSupabase';
import { formatCurrency, formatRelativeTime, getChangeColor } from '@/lib/utils';
import { DEGEN_EMOJIS } from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';

export default function TradeHistoryPage() {
  const [activeFilter, setActiveFilter] = useState('all');
  const [timeRange, setTimeRange] = useState('7d');
  const { containerRef } = useStaggeredEntrance(6, 100);

  const filters = [
    { id: 'all', name: 'All Trades', icon: 'trading' },
    { id: 'buy', name: 'Ape Ins', icon: 'chartUp' },
    { id: 'sell', name: 'Exits', icon: 'chartDown' },
    { id: 'profit', name: 'Gains', icon: 'target' },
    { id: 'loss', name: 'Rekt', icon: 'warning' },
  ];

  const timeRanges = [
    { id: '24h', name: '24H' },
    { id: '7d', name: '7D' },
    { id: '30d', name: '30D' },
    { id: 'all', name: 'All Time' },
  ];



  // Use real trading history data from Supabase
  const { data: tradingHistory } = useTradingHistory();
  const allTrades = tradingHistory || [];

  const filteredTrades = allTrades.filter(trade => {
    if (activeFilter === 'all') return true;
    if (activeFilter === 'buy' && trade.type === 'buy') return true;
    if (activeFilter === 'sell' && trade.type === 'sell') return true;
    if (activeFilter === 'profit' && trade.pnl > 0) return true;
    if (activeFilter === 'loss' && trade.pnl < 0) return true;
    return false;
  });

  const totalPnL = allTrades.reduce((sum, trade) => sum + trade.pnl, 0);
  const totalTrades = allTrades.length;
  const winRate = totalTrades > 0 ? allTrades.filter(t => t.pnl > 0).length / totalTrades * 100 : 0;

  return (
    <MainLayout>
      <div className="space-y-6" ref={containerRef as React.RefObject<HTMLDivElement>}>
        {/* Header */}
        <div className="text-center slide-up-enter">
          <ShimmerText speed="slow">
            <h1 className="text-4xl font-bold text-primary">
              Trade History
            </h1>
          </ShimmerText>
          <p className="mt-3 text-lg text-degen-purple font-medium fade-in-enter">
            Track your diamond hands journey {DEGEN_EMOJIS.diamond} {DEGEN_EMOJIS.rocket}
          </p>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 fade-in-enter">
          <BreathingCard intensity="subtle">
            <Card className="bg-gradient-sophisticated border-gradient-blue text-center">
              <CardContent className="p-6">
                <div className="text-3xl mb-2">{DEGEN_EMOJIS.chart}</div>
                <h3 className="text-lg font-bold text-degen-blue mb-1">Total P&L</h3>
                <p className={`text-2xl font-bold ${getChangeColor(totalPnL)}`}>
                  {totalPnL >= 0 ? '+' : ''}{formatCurrency(totalPnL)}
                </p>
                <p className="text-degen-gray text-sm mt-1">
                  {totalPnL >= 0 ? 'Gains' : 'Down Bad'} {totalPnL >= 0 ? DEGEN_EMOJIS.rocket : DEGEN_EMOJIS.chartDown}
                </p>
              </CardContent>
            </Card>
          </BreathingCard>

          <BreathingCard intensity="subtle">
            <Card className="bg-gradient-sophisticated border-gradient-purple text-center">
              <CardContent className="p-6">
                <div className="text-3xl mb-2">{DEGEN_EMOJIS.target}</div>
                <h3 className="text-lg font-bold text-degen-purple mb-1">Win Rate</h3>
                <p className="text-2xl font-bold text-degen-green">
                  {winRate.toFixed(1)}%
                </p>
                <p className="text-degen-gray text-sm mt-1">
                  {totalTrades} total trades
                </p>
              </CardContent>
            </Card>
          </BreathingCard>

          <BreathingCard intensity="subtle">
            <Card className="bg-gradient-sophisticated border-gradient-green text-center">
              <CardContent className="p-6">
                <div className="text-3xl mb-2">{DEGEN_EMOJIS.fire}</div>
                <h3 className="text-lg font-bold text-degen-green mb-1">Best Trade</h3>
                <p className="text-2xl font-bold text-degen-green">
                  +{formatCurrency(allTrades.length > 0 ? Math.max(...allTrades.map(t => t.pnl)) : 0)}
                </p>
                <p className="text-degen-gray text-sm mt-1">
                  Diamond hands {DEGEN_EMOJIS.diamond}
                </p>
              </CardContent>
            </Card>
          </BreathingCard>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4 justify-between items-center fade-in-enter">
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <Button
                key={filter.id}
                variant={activeFilter === filter.id ? "ape" : "outline"}
                size="sm"
                onClick={() => setActiveFilter(filter.id)}
                className={`font-medium ${activeFilter === filter.id ? 'glow-purple' : ''}`}
              >
                <Icon name={filter.icon} size="sm" color={activeFilter === filter.id ? "white" : "gray"} className="mr-1" />
                {filter.name}
              </Button>
            ))}
          </div>

          <div className="flex gap-2">
            {timeRanges.map((range) => (
              <Button
                key={range.id}
                variant={timeRange === range.id ? "diamond" : "outline"}
                size="sm"
                onClick={() => setTimeRange(range.id)}
                className="font-medium"
              >
                {range.name}
              </Button>
            ))}
          </div>
        </div>

        {/* Trade List */}
        <div className="space-y-4 fade-in-enter">
          {filteredTrades.length === 0 ? (
            <Card className="bg-gradient-degen border-degen-blue/30">
              <CardContent className="p-8 text-center">
                <div className="text-6xl mb-4">{DEGEN_EMOJIS.eyes}</div>
                <h3 className="text-xl font-bold text-degen-blue mb-2">No Trades Found</h3>
                <p className="text-degen-gray">
                  No trades match your current filters. Try adjusting your selection.
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredTrades.map((trade, index) => (
              <BreathingCard key={trade.id} intensity="subtle" className="scale-in-enter">
                <Card className="bg-gradient-degen border-degen-purple/30 hover:glow-purple transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className={`p-3 rounded-full ${trade.type === 'buy' ? 'bg-degen-green/20' : 'bg-degen-red/20'}`}>
                          <Icon 
                            name={trade.type === 'buy' ? 'chartUp' : 'chartDown'} 
                            size="md" 
                            color={trade.type === 'buy' ? 'green' : 'red'} 
                          />
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="text-lg font-bold text-degen-gold">
                              {trade.type === 'buy' ? 'Aped Into' : 'Exited'} ${trade.tokenSymbol}
                            </h3>
                            <Badge variant={trade.type === 'buy' ? 'moon' : 'rekt'} size="sm">
                              {trade.type === 'buy' ? 'BUY' : 'SELL'}
                            </Badge>
                          </div>
                          <p className="text-degen-gray text-sm font-mono">
                            {trade.tokenAddress.slice(0, 8)}...{trade.tokenAddress.slice(-8)}
                          </p>
                          <p className="text-degen-blue text-sm">
                            {formatRelativeTime(trade.timestamp)}
                          </p>
                        </div>
                      </div>

                      <div className="text-right">
                        <div className="flex items-center gap-4">
                          <div>
                            <p className="text-degen-gray text-sm">Amount</p>
                            <p className="font-bold text-degen-blue">
                              {trade.amount} SOL
                            </p>
                          </div>
                          <div>
                            <p className="text-degen-gray text-sm">Value</p>
                            <p className="font-bold text-degen-blue">
                              {formatCurrency(trade.value)}
                            </p>
                          </div>
                          <div>
                            <p className="text-degen-gray text-sm">P&L</p>
                            <p className={`font-bold text-lg ${getChangeColor(trade.pnl)}`}>
                              {trade.pnl >= 0 ? '+' : ''}{formatCurrency(trade.pnl)}
                            </p>
                            <p className={`text-sm ${getChangeColor(trade.pnl)}`}>
                              ({trade.pnl >= 0 ? '+' : ''}{trade.pnlPercentage.toFixed(1)}%)
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </BreathingCard>
            ))
          )}
        </div>

        {/* Load More */}
        {filteredTrades.length > 0 && (
          <div className="text-center fade-in-enter">
            <Button variant="outline" className="font-bold">
              <Icon name="chartUp" size="sm" color="gray" className="mr-1" />
              Load More Trades
            </Button>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
