'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { DatabaseSetup } from '@/lib/supabase/setup';
import { SignalService } from '@/lib/supabase/services/signalService';

export default function SetupDatabasePage() {
  const [setupStatus, setSetupStatus] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [setupSQL, setSetupSQL] = useState('');
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const verifyDatabaseSetup = async () => {
    setIsLoading(true);
    addTestResult('🔍 Starting database verification...');
    
    try {
      const status = await DatabaseSetup.verifySetup();
      setSetupStatus(status);
      setSetupSQL(status.setupSQL);
      
      addTestResult(`📊 Database verification completed`);
      addTestResult(`✅ Tables found: ${Object.values(status.tablesExist).filter(Boolean).length}/${Object.keys(status.tablesExist).length}`);
      
      if (status.allTablesExist) {
        addTestResult('🎉 All required tables exist!');
      } else {
        addTestResult('❌ Some tables are missing - check setup instructions below');
      }
    } catch (error) {
      addTestResult(`❌ Error during verification: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('Database verification error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const testSignalService = async () => {
    addTestResult('🧪 Testing SignalService.getAllChannels...');
    
    try {
      const channels = await SignalService.getAllChannels();
      addTestResult(`✅ SignalService test successful - got ${channels.length} channels`);
      
      if (channels.length > 0) {
        addTestResult(`📋 Sample channel: ${channels[0].name} (@${channels[0].username})`);
      }
    } catch (error) {
      addTestResult(`❌ SignalService test failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      console.error('SignalService test error:', error);
    }
  };

  const insertDemoData = async () => {
    addTestResult('📝 Inserting demo data...');
    
    try {
      const success = await DatabaseSetup.insertDemoData();
      if (success) {
        addTestResult('✅ Demo data inserted successfully');
        // Re-verify setup to update status
        await verifyDatabaseSetup();
      } else {
        addTestResult('❌ Failed to insert demo data');
      }
    } catch (error) {
      addTestResult(`❌ Error inserting demo data: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const copySetupSQL = () => {
    navigator.clipboard.writeText(setupSQL);
    addTestResult('📋 Setup SQL copied to clipboard');
  };

  useEffect(() => {
    verifyDatabaseSetup();
  }, []);

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Database Setup & Diagnostics
          </h1>
          <p className="text-gray-400">
            Verify and setup your Supabase database for Signal V1
          </p>
        </div>

        {/* Setup Status */}
        <Card>
          <CardHeader>
            <CardTitle>Database Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500"></div>
                <span className="ml-2 text-gray-400">Checking database...</span>
              </div>
            ) : setupStatus ? (
              <div className="space-y-4">
                <div className={`p-4 rounded-lg ${setupStatus.allTablesExist ? 'bg-green-900/50 border border-green-500/50' : 'bg-red-900/50 border border-red-500/50'}`}>
                  <h3 className={`font-bold ${setupStatus.allTablesExist ? 'text-green-400' : 'text-red-400'}`}>
                    {setupStatus.allTablesExist ? '✅ Database Ready' : '❌ Database Setup Required'}
                  </h3>
                  <p className="text-sm mt-1 text-gray-300">
                    {setupStatus.allTablesExist 
                      ? 'All required tables exist and are accessible'
                      : 'Some required tables are missing from your Supabase database'
                    }
                  </p>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  {Object.entries(setupStatus.tablesExist).map(([tableName, exists]) => (
                    <div key={tableName} className="flex items-center justify-between p-3 bg-gray-800 rounded-lg">
                      <span className="font-mono text-sm">{tableName}</span>
                      <span className={`font-bold ${exists ? 'text-green-400' : 'text-red-400'}`}>
                        {exists ? '✅' : '❌'}
                      </span>
                    </div>
                  ))}
                </div>

                <div className="space-y-2">
                  <h4 className="font-semibold text-white">Recommendations:</h4>
                  {setupStatus.recommendations.map((rec: string, index: number) => (
                    <p key={index} className="text-sm text-gray-300">
                      {rec}
                    </p>
                  ))}
                </div>
              </div>
            ) : (
              <p className="text-gray-400">Click "Verify Database" to check setup status</p>
            )}
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button onClick={verifyDatabaseSetup} variant="outline" disabled={isLoading}>
                Verify Database
              </Button>
              <Button onClick={testSignalService} variant="outline">
                Test SignalService
              </Button>
              <Button onClick={insertDemoData} variant="outline">
                Insert Demo Data
              </Button>
              <Button onClick={() => setTestResults([])} variant="outline">
                Clear Results
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Setup SQL */}
        {setupSQL && (
          <Card>
            <CardHeader>
              <CardTitle>Database Setup SQL</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-400 text-sm">
                Copy this SQL script and run it in your Supabase SQL editor to create the required tables:
              </p>
              <div className="relative">
                <pre className="bg-gray-800 p-4 rounded text-xs overflow-auto max-h-64 border">
                  <code>{setupSQL}</code>
                </pre>
                <Button 
                  onClick={copySetupSQL}
                  size="sm"
                  className="absolute top-2 right-2"
                >
                  Copy SQL
                </Button>
              </div>
              <div className="flex gap-2">
                <Button 
                  onClick={() => window.open('https://supabase.com/dashboard', '_blank')}
                  variant="outline"
                  size="sm"
                >
                  Open Supabase Dashboard
                </Button>
                <Button 
                  onClick={() => window.open('https://supabase.com/dashboard/project/ccbfqeollvwzrwkzkilz/sql', '_blank')}
                  variant="outline"
                  size="sm"
                >
                  Open SQL Editor
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-800 p-4 rounded max-h-64 overflow-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-400">No test results yet...</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Links */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Links</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
              <Button 
                onClick={() => window.location.href = '/settings'}
                variant="outline"
                size="sm"
              >
                Settings Page
              </Button>
              <Button 
                onClick={() => window.location.href = '/signals'}
                variant="outline"
                size="sm"
              >
                Signals Page
              </Button>
              <Button 
                onClick={() => window.location.href = '/test-wallet-state'}
                variant="outline"
                size="sm"
              >
                Wallet Test
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
