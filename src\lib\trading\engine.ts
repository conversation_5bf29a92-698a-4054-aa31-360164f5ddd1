// Trading Engine - Handles automated trading logic and execution

import { Connection, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';
import { JupiterAPI } from '../jupiter/api';
import { PortfolioService } from '../supabase/services/portfolioService';
import { UserService } from '../supabase/services/userService';
import { SOLANA_CONFIG, TRADING_CONFIG, ERROR_MESSAGES } from '../constants';
import { lamportsToSol, solToLamports, calculatePercentageChange } from '../utils';
import type { ParsedSignal, Trade, Position, TradingSettings } from '@/types';

export interface TradeRequest {
  tokenAddress: string;
  tokenSymbol?: string;
  tokenName?: string;
  action: 'buy' | 'sell';
  amount: number; // in SOL for buy, in token amount for sell
  slippage?: number;
  signalId?: string;
  positionId?: string;
}

export interface TradeResult {
  success: boolean;
  signature?: string;
  error?: string;
  trade?: Trade;
  position?: Position;
}

export class TradingEngine {
  private connection: Connection;
  private isEnabled = false;

  constructor() {
    this.connection = new Connection(SOLANA_CONFIG.rpcUrl, SOLANA_CONFIG.commitment);
  }

  /**
   * Enable trading engine
   */
  enable(): void {
    this.isEnabled = true;
    console.log('Trading engine enabled');
  }

  /**
   * Disable trading engine
   */
  disable(): void {
    this.isEnabled = false;
    console.log('Trading engine disabled');
  }

  /**
   * Check if trading is enabled
   */
  isEngineEnabled(): boolean {
    return this.isEnabled;
  }

  /**
   * Process a trading signal automatically
   */
  async processSignal(
    signal: ParsedSignal,
    userPublicKey: PublicKey,
    signTransaction: (transaction: Transaction | VersionedTransaction) => Promise<Transaction | VersionedTransaction>
  ): Promise<TradeResult> {
    try {
      if (!this.isEnabled) {
        return {
          success: false,
          error: 'Trading engine is disabled',
        };
      }

      // Get user trading settings
      const userProfile = await UserService.getCurrentUserProfile();
      if (!userProfile) {
        return {
          success: false,
          error: 'User profile not found',
        };
      }

      const tradingSettings = userProfile.preferences.trading;

      // Check if auto-trading is enabled
      if (!tradingSettings.autoTrade) {
        return {
          success: false,
          error: 'Auto-trading is disabled',
        };
      }

      // Validate signal confidence
      if (signal.confidence < 0.6) {
        return {
          success: false,
          error: 'Signal confidence too low',
        };
      }

      // Check position limits
      const activePositions = await PortfolioService.getActivePositions();
      if (activePositions.length >= tradingSettings.maxActivePositions) {
        return {
          success: false,
          error: ERROR_MESSAGES.trading.maxPositionsReached,
        };
      }

      // Check if we already have a position for this token
      const existingPosition = activePositions.find(p => p.tokenAddress === signal.tokenAddress);
      if (existingPosition) {
        return {
          success: false,
          error: 'Position already exists for this token',
        };
      }

      // Execute buy trade
      const tradeRequest: TradeRequest = {
        tokenAddress: signal.tokenAddress,
        tokenSymbol: signal.tokenSymbol,
        tokenName: signal.tokenSymbol || 'Unknown Token',
        action: 'buy',
        amount: tradingSettings.defaultAmount,
        slippage: tradingSettings.slippageTolerance,
        signalId: signal.metadata.channelId, // Using channelId as signalId for now
      };

      return await this.executeTrade(tradeRequest, userPublicKey, signTransaction);
    } catch (error) {
      console.error('Error processing signal:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Execute a trade
   */
  async executeTrade(
    request: TradeRequest,
    userPublicKey: PublicKey,
    signTransaction: (transaction: Transaction | VersionedTransaction) => Promise<Transaction | VersionedTransaction>
  ): Promise<TradeResult> {
    try {
      // Validate request
      const validation = this.validateTradeRequest(request);
      if (!validation.valid) {
        return {
          success: false,
          error: validation.error,
        };
      }

      // Get user balance
      const balance = await this.connection.getBalance(userPublicKey);
      const solBalance = lamportsToSol(balance);

      if (request.action === 'buy') {
        // Check if user has enough SOL
        if (solBalance < request.amount) {
          return {
            success: false,
            error: ERROR_MESSAGES.wallet.insufficientBalance,
          };
        }

        return await this.executeBuyTrade(request, userPublicKey, signTransaction);
      } else {
        return await this.executeSellTrade(request, userPublicKey, signTransaction);
      }
    } catch (error) {
      console.error('Error executing trade:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : ERROR_MESSAGES.wallet.transactionFailed,
      };
    }
  }

  /**
   * Execute buy trade
   */
  private async executeBuyTrade(
    request: TradeRequest,
    userPublicKey: PublicKey,
    signTransaction: (transaction: Transaction | VersionedTransaction) => Promise<Transaction | VersionedTransaction>
  ): Promise<TradeResult> {
    try {
      const inputMint = 'So11111111111111111111111111111111111111112'; // SOL
      const outputMint = request.tokenAddress;
      const amountInLamports = solToLamports(request.amount);
      const slippageBps = (request.slippage || TRADING_CONFIG.defaultTradeAmount) * 100;

      // Get quote from Jupiter
      const quote = await JupiterAPI.getQuote(
        inputMint,
        outputMint,
        amountInLamports,
        slippageBps
      );

      if (!quote) {
        return {
          success: false,
          error: 'Failed to get trading quote',
        };
      }

      // Get swap transaction
      const swapTransaction = await JupiterAPI.getSwapTransaction(
        quote,
        userPublicKey.toString()
      );

      if (!swapTransaction) {
        return {
          success: false,
          error: 'Failed to create swap transaction',
        };
      }

      // Deserialize and sign transaction
      const transaction = VersionedTransaction.deserialize(
        Buffer.from(swapTransaction.swapTransaction, 'base64')
      );

      const signedTransaction = await signTransaction(transaction);

      // Send transaction
      const signature = await this.connection.sendRawTransaction(
        signedTransaction.serialize(),
        {
          skipPreflight: false,
          preflightCommitment: 'confirmed',
        }
      );

      // Wait for confirmation
      await this.connection.confirmTransaction(signature, 'confirmed');

      // Calculate trade details
      const outputAmount = parseFloat(quote.outAmount) / Math.pow(10, 6); // Assuming 6 decimals
      const price = request.amount / outputAmount;
      const fees = 0.0025; // Estimate 0.25% fees

      // Create trade record
      const trade: Omit<Trade, 'id' | 'createdAt'> = {
        portfolioId: '', // Will be set by service
        positionId: request.positionId,
        type: 'buy',
        tokenAddress: request.tokenAddress,
        tokenSymbol: request.tokenSymbol || 'UNKNOWN',
        amount: outputAmount,
        price,
        value: request.amount,
        slippage: parseFloat(quote.priceImpactPct) || 0,
        fees,
        signature,
        status: 'confirmed',
        timestamp: new Date(),
        signalId: request.signalId,
      };

      // Record trade in database
      const savedTrade = await PortfolioService.recordTrade(trade);

      // Create or update position
      let position: Position | null = null;
      
      if (!request.positionId) {
        // Create new position
        const newPosition: Omit<Position, 'id' | 'createdAt' | 'updatedAt'> = {
          portfolioId: '', // Will be set by service
          tokenAddress: request.tokenAddress,
          tokenSymbol: request.tokenSymbol || 'UNKNOWN',
          tokenName: request.tokenName || 'Unknown Token',
          amount: outputAmount,
          entryPrice: price,
          currentPrice: price,
          entryValue: request.amount,
          currentValue: request.amount,
          pnl: 0,
          pnlPercentage: 0,
          multiplier: 1,
          status: 'active',
          entryDate: new Date(),
          signalId: request.signalId,
        };

        position = await PortfolioService.createPosition(newPosition);
      }

      return {
        success: true,
        signature,
        trade: savedTrade || undefined,
        position: position || undefined,
      };
    } catch (error) {
      console.error('Error executing buy trade:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Buy trade failed',
      };
    }
  }

  /**
   * Execute sell trade
   */
  private async executeSellTrade(
    request: TradeRequest,
    userPublicKey: PublicKey,
    signTransaction: (transaction: Transaction | VersionedTransaction) => Promise<Transaction | VersionedTransaction>
  ): Promise<TradeResult> {
    try {
      // Implementation similar to buy trade but reversed
      // This would involve getting token balance, creating sell quote, etc.
      
      return {
        success: false,
        error: 'Sell trade not implemented yet',
      };
    } catch (error) {
      console.error('Error executing sell trade:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Sell trade failed',
      };
    }
  }

  /**
   * Validate trade request
   */
  private validateTradeRequest(request: TradeRequest): { valid: boolean; error?: string } {
    if (!request.tokenAddress || request.tokenAddress.length !== 44) {
      return { valid: false, error: ERROR_MESSAGES.trading.tokenNotFound };
    }

    if (request.amount <= 0) {
      return { valid: false, error: ERROR_MESSAGES.trading.invalidAmount };
    }

    if (request.slippage && (request.slippage < 0.1 || request.slippage > 50)) {
      return { valid: false, error: 'Invalid slippage tolerance' };
    }

    return { valid: true };
  }

  /**
   * Check if position should be closed based on profit-taking rules
   */
  async checkProfitTaking(position: Position): Promise<{ shouldSell: boolean; percentage: number }> {
    try {
      const userProfile = await UserService.getCurrentUserProfile();
      if (!userProfile?.preferences.trading.profitTakingEnabled) {
        return { shouldSell: false, percentage: 0 };
      }

      // Check profit-taking levels
      for (const level of TRADING_CONFIG.profitTakingLevels) {
        if (position.pnlPercentage >= level.percentage) {
          return { shouldSell: true, percentage: level.sellPercentage };
        }
      }

      // Check stop-loss
      const stopLossPercentage = userProfile.preferences.trading.stopLossPercentage;
      if (position.pnlPercentage <= -stopLossPercentage) {
        return { shouldSell: true, percentage: 100 }; // Sell everything on stop-loss
      }

      return { shouldSell: false, percentage: 0 };
    } catch (error) {
      console.error('Error checking profit taking:', error);
      return { shouldSell: false, percentage: 0 };
    }
  }
}
