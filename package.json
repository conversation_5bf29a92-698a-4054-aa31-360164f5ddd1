{"name": "signal-v1", "version": "0.1.0", "private": true, "description": "Signal V1 - Comprehensive mobile-first trading application integrating Telegram signal monitoring with automated Solana trading via Jupiter DEX", "keywords": ["solana", "trading", "telegram", "jupiter", "dex", "signals", "crypto"], "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@jup-ag/api": "^6.0.42", "@jup-ag/react-hook": "^6.1.2", "@solana/wallet-adapter-backpack": "^0.1.14", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-glow": "^0.1.18", "@solana/wallet-adapter-phantom": "^0.9.28", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/wallet-adapter-solflare": "^0.6.32", "@solana/web3.js": "^1.98.2", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.80.10", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.18.1", "lucide-react": "^0.518.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/node-telegram-bot-api": "^0.64.9", "@types/react": "^19", "@types/react-dom": "^19", "babel-jest": "^29.7.0", "eslint": "^9", "eslint-config-next": "15.3.4", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "node-telegram-bot-api": "^0.66.0", "tailwindcss": "^4", "typescript": "^5"}}