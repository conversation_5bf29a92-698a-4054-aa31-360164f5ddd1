'use client';

import React from 'react';
import { Card, CardContent, CardHeader } from '@/components/ui/Card';
import { Badge } from '@/components/ui/Badge';
import { Button } from '@/components/ui/Button';
import { PulseDot } from '@/components/ui/LiveIndicator';
import Icon from '@/components/ui/Icon';
import { formatRelativeTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface TicketCardProps {
  ticket: {
    id: string;
    ticket_number: string;
    title: string;
    description: string;
    category: 'technical' | 'trading' | 'account' | 'feature_request' | 'general';
    priority: 'low' | 'medium' | 'high' | 'critical';
    status: 'open' | 'in_progress' | 'resolved' | 'closed';
    created_at: string;
    updated_at: string;
    resolved_at?: string | null;
  };
  onViewDetails?: (ticketId: string) => void;
  onClose?: (ticketId: string) => void;
}

export function TicketCard({ ticket, onViewDetails, onClose }: TicketCardProps) {
  const categoryConfig = {
    technical: { icon: 'settings', color: 'text-degen-red', label: 'Technical' },
    trading: { icon: 'trading', color: 'text-degen-green', label: 'Trading' },
    account: { icon: 'wallet', color: 'text-degen-blue', label: 'Account' },
    feature_request: { icon: 'rocket', color: 'text-degen-purple', label: 'Feature' },
    general: { icon: 'target', color: 'text-degen-gold', label: 'General' },
  };

  const priorityConfig = {
    low: { color: 'bg-degen-gray', textColor: 'text-white', label: 'Low' },
    medium: { color: 'bg-degen-blue', textColor: 'text-white', label: 'Medium' },
    high: { color: 'bg-degen-gold', textColor: 'text-black', label: 'High' },
    critical: { color: 'bg-degen-red', textColor: 'text-white', label: 'Critical' },
  };

  const statusConfig = {
    open: { 
      color: 'text-degen-blue', 
      bgColor: 'bg-degen-blue/20', 
      borderColor: 'border-degen-blue/30',
      label: 'Open',
      dot: 'blue' as const
    },
    in_progress: { 
      color: 'text-degen-gold', 
      bgColor: 'bg-degen-gold/20', 
      borderColor: 'border-degen-gold/30',
      label: 'In Progress',
      dot: 'gold' as const
    },
    resolved: { 
      color: 'text-degen-green', 
      bgColor: 'bg-degen-green/20', 
      borderColor: 'border-degen-green/30',
      label: 'Resolved',
      dot: 'green' as const
    },
    closed: { 
      color: 'text-degen-gray', 
      bgColor: 'bg-degen-gray/20', 
      borderColor: 'border-degen-gray/30',
      label: 'Closed',
      dot: 'gray' as const
    },
  };

  const category = categoryConfig[ticket.category];
  const priority = priorityConfig[ticket.priority];
  const status = statusConfig[ticket.status];

  const truncateDescription = (text: string, maxLength: number = 120) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <Card className={cn(
      'bg-gradient-sophisticated transition-all duration-300 hover:glow-purple hover-lift',
      status.borderColor
    )}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-2">
              <Badge 
                variant="outline" 
                className={cn('text-xs font-mono', status.color, status.bgColor)}
              >
                <PulseDot color={status.dot} size="sm" className="mr-1" />
                {ticket.ticket_number}
              </Badge>
              <Badge 
                className={cn('text-xs', priority.color, priority.textColor)}
              >
                {priority.label}
              </Badge>
            </div>
            <h3 className="font-bold text-primary text-sm leading-tight mb-1">
              {ticket.title}
            </h3>
            <div className="flex items-center gap-2 text-xs text-degen-gray">
              <Icon name={category.icon} size="sm" color="gray" />
              <span className={category.color}>{category.label}</span>
              <span>•</span>
              <span>Created {formatRelativeTime(ticket.created_at)}</span>
            </div>
          </div>
          <div className={cn(
            'flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium',
            status.bgColor,
            status.color
          )}>
            <PulseDot color={status.dot} size="sm" />
            {status.label}
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        <p className="text-sm text-degen-gray mb-4 leading-relaxed">
          {truncateDescription(ticket.description)}
        </p>

        <div className="flex items-center justify-between">
          <div className="text-xs text-degen-gray">
            {ticket.status === 'resolved' && ticket.resolved_at && (
              <span>Resolved {formatRelativeTime(ticket.resolved_at)}</span>
            )}
            {ticket.status !== 'resolved' && (
              <span>Updated {formatRelativeTime(ticket.updated_at)}</span>
            )}
          </div>

          <div className="flex items-center gap-2">
            {onViewDetails && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onViewDetails(ticket.id)}
                className="text-xs"
              >
                <Icon name="eye" size="sm" color="gray" className="mr-1" />
                View
              </Button>
            )}
            
            {onClose && ticket.status !== 'closed' && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => onClose(ticket.id)}
                className="text-xs text-degen-red hover:text-red-400"
              >
                <Icon name="x" size="sm" color="red" className="mr-1" />
                Close
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
