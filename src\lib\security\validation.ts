// Security Validation - Input validation and sanitization

import { VALIDATION_RULES } from '../constants';

export class SecurityValidator {
  /**
   * Validate Solana address format
   */
  static validateSolanaAddress(address: string): { valid: boolean; error?: string } {
    if (!address) {
      return { valid: false, error: 'Address is required' };
    }

    if (typeof address !== 'string') {
      return { valid: false, error: 'Address must be a string' };
    }

    if (address.length !== VALIDATION_RULES.solanaAddress.length) {
      return { valid: false, error: `Address must be ${VALIDATION_RULES.solanaAddress.length} characters long` };
    }

    if (!VALIDATION_RULES.solanaAddress.pattern.test(address)) {
      return { valid: false, error: 'Invalid address format' };
    }

    return { valid: true };
  }

  /**
   * Validate trade amount
   */
  static validateTradeAmount(amount: number): { valid: boolean; error?: string } {
    if (typeof amount !== 'number' || isNaN(amount)) {
      return { valid: false, error: 'Amount must be a valid number' };
    }

    if (amount <= 0) {
      return { valid: false, error: 'Amount must be greater than 0' };
    }

    if (amount < VALIDATION_RULES.tradeAmount.min) {
      return { valid: false, error: `Minimum trade amount is ${VALIDATION_RULES.tradeAmount.min} SOL` };
    }

    if (amount > VALIDATION_RULES.tradeAmount.max) {
      return { valid: false, error: `Maximum trade amount is ${VALIDATION_RULES.tradeAmount.max} SOL` };
    }

    return { valid: true };
  }

  /**
   * Validate slippage percentage
   */
  static validateSlippage(slippage: number): { valid: boolean; error?: string } {
    if (typeof slippage !== 'number' || isNaN(slippage)) {
      return { valid: false, error: 'Slippage must be a valid number' };
    }

    if (slippage < VALIDATION_RULES.slippage.min) {
      return { valid: false, error: `Minimum slippage is ${VALIDATION_RULES.slippage.min}%` };
    }

    if (slippage > VALIDATION_RULES.slippage.max) {
      return { valid: false, error: `Maximum slippage is ${VALIDATION_RULES.slippage.max}%` };
    }

    return { valid: true };
  }

  /**
   * Validate email format
   */
  static validateEmail(email: string): { valid: boolean; error?: string } {
    if (!email) {
      return { valid: false, error: 'Email is required' };
    }

    if (typeof email !== 'string') {
      return { valid: false, error: 'Email must be a string' };
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return { valid: false, error: 'Invalid email format' };
    }

    if (email.length > 254) {
      return { valid: false, error: 'Email is too long' };
    }

    return { valid: true };
  }

  /**
   * Sanitize string input
   */
  static sanitizeString(input: string): string {
    if (typeof input !== 'string') {
      return '';
    }

    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .substring(0, 1000); // Limit length
  }

  /**
   * Validate API key format
   */
  static validateApiKey(apiKey: string): { valid: boolean; error?: string } {
    if (!apiKey) {
      return { valid: false, error: 'API key is required' };
    }

    if (typeof apiKey !== 'string') {
      return { valid: false, error: 'API key must be a string' };
    }

    if (apiKey.length < 10) {
      return { valid: false, error: 'API key is too short' };
    }

    if (apiKey.length > 200) {
      return { valid: false, error: 'API key is too long' };
    }

    return { valid: true };
  }

  /**
   * Validate percentage value
   */
  static validatePercentage(percentage: number, min: number = 0, max: number = 100): { valid: boolean; error?: string } {
    if (typeof percentage !== 'number' || isNaN(percentage)) {
      return { valid: false, error: 'Percentage must be a valid number' };
    }

    if (percentage < min) {
      return { valid: false, error: `Percentage must be at least ${min}%` };
    }

    if (percentage > max) {
      return { valid: false, error: `Percentage cannot exceed ${max}%` };
    }

    return { valid: true };
  }

  /**
   * Validate URL format
   */
  static validateUrl(url: string): { valid: boolean; error?: string } {
    if (!url) {
      return { valid: false, error: 'URL is required' };
    }

    try {
      const urlObj = new URL(url);
      
      // Only allow HTTPS in production
      if (process.env.NODE_ENV === 'production' && urlObj.protocol !== 'https:') {
        return { valid: false, error: 'Only HTTPS URLs are allowed in production' };
      }

      // Check for allowed protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { valid: false, error: 'Invalid URL protocol' };
      }

      return { valid: true };
    } catch (error) {
      return { valid: false, error: 'Invalid URL format' };
    }
  }

  /**
   * Rate limiting check
   */
  static checkRateLimit(identifier: string, maxRequests: number = 100, windowMs: number = 60000): boolean {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    // Get or create request history for this identifier
    const key = `rate_limit_${identifier}`;
    const requestHistory = JSON.parse(localStorage.getItem(key) || '[]') as number[];
    
    // Filter out old requests
    const recentRequests = requestHistory.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    if (recentRequests.length >= maxRequests) {
      return false;
    }
    
    // Add current request
    recentRequests.push(now);
    localStorage.setItem(key, JSON.stringify(recentRequests));
    
    return true;
  }

  /**
   * Validate trading settings
   */
  static validateTradingSettings(settings: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate default amount
    const amountValidation = this.validateTradeAmount(settings.defaultAmount);
    if (!amountValidation.valid) {
      errors.push(`Default amount: ${amountValidation.error}`);
    }

    // Validate max position percentage
    const positionValidation = this.validatePercentage(settings.maxPositionPercentage, 1, 50);
    if (!positionValidation.valid) {
      errors.push(`Max position percentage: ${positionValidation.error}`);
    }

    // Validate stop loss percentage
    const stopLossValidation = this.validatePercentage(settings.stopLossPercentage, 1, 90);
    if (!stopLossValidation.valid) {
      errors.push(`Stop loss percentage: ${stopLossValidation.error}`);
    }

    // Validate slippage tolerance
    const slippageValidation = this.validateSlippage(settings.slippageTolerance);
    if (!slippageValidation.valid) {
      errors.push(`Slippage tolerance: ${slippageValidation.error}`);
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Validate signal data
   */
  static validateSignalData(signal: any): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate token address
    if (signal.tokenAddress) {
      const addressValidation = this.validateSolanaAddress(signal.tokenAddress);
      if (!addressValidation.valid) {
        errors.push(`Token address: ${addressValidation.error}`);
      }
    }

    // Validate token symbol
    if (signal.tokenSymbol) {
      if (typeof signal.tokenSymbol !== 'string' || signal.tokenSymbol.length > 10) {
        errors.push('Token symbol must be a string with max 10 characters');
      }
    }

    // Validate market cap
    if (signal.marketCap !== undefined) {
      if (typeof signal.marketCap !== 'number' || signal.marketCap < 0) {
        errors.push('Market cap must be a positive number');
      }
    }

    // Validate content
    if (!signal.content || typeof signal.content !== 'string') {
      errors.push('Signal content is required and must be a string');
    } else if (signal.content.length > 5000) {
      errors.push('Signal content is too long (max 5000 characters)');
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Check for suspicious patterns
   */
  static checkSuspiciousPatterns(content: string): { suspicious: boolean; reasons: string[] } {
    const reasons: string[] = [];
    const lowerContent = content.toLowerCase();

    // Check for scam keywords
    const scamKeywords = [
      'guaranteed profit',
      'risk free',
      'send me money',
      'private message me',
      'limited time',
      'act fast',
      'last chance',
      'don\'t miss',
      '100% profit',
      'no risk',
    ];

    for (const keyword of scamKeywords) {
      if (lowerContent.includes(keyword)) {
        reasons.push(`Contains suspicious keyword: "${keyword}"`);
      }
    }

    // Check for excessive emojis
    const emojiCount = (content.match(/[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]/gu) || []).length;
    if (emojiCount > 10) {
      reasons.push('Excessive emoji usage');
    }

    // Check for excessive capitalization
    const capsCount = (content.match(/[A-Z]/g) || []).length;
    const totalLetters = (content.match(/[A-Za-z]/g) || []).length;
    if (totalLetters > 0 && (capsCount / totalLetters) > 0.5) {
      reasons.push('Excessive capitalization');
    }

    // Check for suspicious URLs
    const urlPattern = /(https?:\/\/[^\s]+)/g;
    const urls = content.match(urlPattern) || [];
    for (const url of urls) {
      if (url.includes('bit.ly') || url.includes('tinyurl') || url.includes('t.me')) {
        reasons.push(`Suspicious URL detected: ${url}`);
      }
    }

    return {
      suspicious: reasons.length > 0,
      reasons,
    };
  }
}
