/**
 * Comprehensive Multiplier Badge Positioning Test
 * Tests all pages to ensure badges don't overlap with other elements
 */

(function() {
  console.log('🔥 Starting Multiplier Badge Positioning Test...');
  
  const testResults = {
    portfolio: { tested: false, passed: false, issues: [] },
    signals: { tested: false, passed: false, issues: [] },
    home: { tested: false, passed: false, issues: [] }
  };

  // Test current page
  function testCurrentPage() {
    const currentPath = window.location.pathname;
    let pageName = 'home';
    
    if (currentPath.includes('/portfolio')) {
      pageName = 'portfolio';
    } else if (currentPath.includes('/signals')) {
      pageName = 'signals';
    }
    
    console.log(`🧪 Testing ${pageName} page...`);
    
    // Find all multiplier badges
    const badges = document.querySelectorAll('[class*="absolute"][class*="top-"][class*="right-"]');
    const flameIcons = document.querySelectorAll('svg[viewBox="0 0 24 24"]');
    const multiplierElements = Array.from(document.querySelectorAll('*')).filter(el => 
      el.textContent && el.textContent.match(/\d+(\.\d+)?[xX]/) && 
      el.className && el.className.includes('absolute')
    );
    
    console.log(`Found ${badges.length} positioned badges, ${flameIcons.length} flame icons, ${multiplierElements.length} multiplier elements`);
    
    const allBadges = [...badges, ...multiplierElements];
    testResults[pageName].tested = true;
    testResults[pageName].issues = [];
    
    if (allBadges.length === 0) {
      console.log(`ℹ️ No multiplier badges found on ${pageName} page`);
      testResults[pageName].passed = true;
      return;
    }
    
    // Test each badge for overlapping
    allBadges.forEach((badge, index) => {
      const badgeRect = badge.getBoundingClientRect();
      
      // Find nearby elements that could overlap
      const parent = badge.closest('[class*="relative"]') || badge.parentElement;
      if (!parent) return;
      
      const siblings = Array.from(parent.querySelectorAll('*')).filter(el => 
        el !== badge && 
        !badge.contains(el) && 
        !el.contains(badge)
      );
      
      let hasOverlap = false;
      const overlappingElements = [];
      
      siblings.forEach(sibling => {
        const siblingRect = sibling.getBoundingClientRect();
        
        // Check for overlap
        const overlap = !(
          badgeRect.right < siblingRect.left || 
          badgeRect.left > siblingRect.right || 
          badgeRect.bottom < siblingRect.top || 
          badgeRect.top > siblingRect.bottom
        );
        
        if (overlap && siblingRect.width > 0 && siblingRect.height > 0) {
          // Check if it's a meaningful overlap (not just touching edges)
          const overlapArea = Math.max(0, Math.min(badgeRect.right, siblingRect.right) - Math.max(badgeRect.left, siblingRect.left)) *
                             Math.max(0, Math.min(badgeRect.bottom, siblingRect.bottom) - Math.max(badgeRect.top, siblingRect.top));
          
          if (overlapArea > 10) { // More than 10px² overlap
            hasOverlap = true;
            overlappingElements.push({
              element: sibling.tagName,
              className: sibling.className,
              textContent: sibling.textContent?.slice(0, 50) || '',
              overlapArea
            });
          }
        }
      });
      
      if (hasOverlap) {
        const issue = `Badge ${index + 1} overlaps with ${overlappingElements.length} element(s)`;
        testResults[pageName].issues.push(issue);
        console.error(`❌ ${issue}:`, overlappingElements);
      } else {
        console.log(`✅ Badge ${index + 1} positioned correctly`);
      }
    });
    
    testResults[pageName].passed = testResults[pageName].issues.length === 0;
    
    if (testResults[pageName].passed) {
      console.log(`✅ ${pageName} page: All badges positioned correctly!`);
    } else {
      console.error(`❌ ${pageName} page: ${testResults[pageName].issues.length} positioning issue(s) found`);
    }
  }
  
  // Test spacing and margins
  function testSpacing() {
    console.log('📏 Testing badge spacing and margins...');
    
    const badges = document.querySelectorAll('[class*="absolute"][class*="top-"][class*="right-"]');
    
    badges.forEach((badge, index) => {
      const styles = window.getComputedStyle(badge);
      const parent = badge.offsetParent;
      
      if (parent) {
        const parentRect = parent.getBoundingClientRect();
        const badgeRect = badge.getBoundingClientRect();
        
        const rightMargin = parentRect.right - badgeRect.right;
        const topMargin = badgeRect.top - parentRect.top;
        
        console.log(`Badge ${index + 1} margins: top=${topMargin}px, right=${rightMargin}px`);
        
        if (rightMargin < 8) {
          console.warn(`⚠️ Badge ${index + 1} has insufficient right margin (${rightMargin}px)`);
        }
        
        if (topMargin < 8) {
          console.warn(`⚠️ Badge ${index + 1} has insufficient top margin (${topMargin}px)`);
        }
      }
    });
  }
  
  // Test responsive behavior
  function testResponsive() {
    console.log('📱 Testing responsive behavior...');
    
    const originalWidth = window.innerWidth;
    const testWidths = [320, 768, 1024, 1440]; // Mobile, tablet, desktop, large desktop
    
    testWidths.forEach(width => {
      // Note: We can't actually resize the window in this context,
      // but we can check if badges have responsive classes
      console.log(`Testing for ${width}px width considerations...`);
      
      const badges = document.querySelectorAll('[class*="absolute"][class*="top-"][class*="right-"]');
      badges.forEach((badge, index) => {
        const hasResponsiveClasses = badge.className.match(/(sm:|md:|lg:|xl:)/);
        if (!hasResponsiveClasses) {
          console.log(`ℹ️ Badge ${index + 1} doesn't use responsive classes (may be intentional)`);
        }
      });
    });
  }
  
  // Run all tests
  testCurrentPage();
  testSpacing();
  testResponsive();
  
  // Summary
  setTimeout(() => {
    console.log('\n📊 MULTIPLIER BADGE TEST SUMMARY:');
    console.log('=====================================');
    
    Object.entries(testResults).forEach(([page, result]) => {
      if (result.tested) {
        const status = result.passed ? '✅ PASS' : '❌ FAIL';
        console.log(`${status} - ${page.toUpperCase()} page`);
        if (result.issues.length > 0) {
          result.issues.forEach(issue => console.log(`  • ${issue}`));
        }
      } else {
        console.log(`⏭️ SKIP - ${page.toUpperCase()} page (not tested)`);
      }
    });
    
    const testedPages = Object.values(testResults).filter(r => r.tested);
    const passedPages = testedPages.filter(r => r.passed);
    
    console.log(`\n🎯 Overall: ${passedPages.length}/${testedPages.length} pages passed`);
    
    if (passedPages.length === testedPages.length) {
      console.log('🎉 All tested pages have correct badge positioning!');
    } else {
      console.log('⚠️ Some pages need badge positioning fixes');
    }
    
    // Instructions for testing other pages
    console.log('\n📋 To test other pages:');
    console.log('1. Navigate to /portfolio and run this script');
    console.log('2. Navigate to /signals and run this script');
    console.log('3. Navigate to / (home) and run this script');
    
    console.log('\n✨ Test completed!');
  }, 1000);
})();
