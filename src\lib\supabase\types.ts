// Supabase Database Types

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string | null
          wallet_address: string | null
          created_at: string
          updated_at: string
          preferences: <PERSON><PERSON> | null
        }
        Insert: {
          id: string
          email?: string | null
          wallet_address?: string | null
          created_at?: string
          updated_at?: string
          preferences?: Json | null
        }
        Update: {
          id?: string
          email?: string | null
          wallet_address?: string | null
          created_at?: string
          updated_at?: string
          preferences?: Json | null
        }
        Relationships: []
      }
      telegram_channels: {
        Row: {
          id: string
          name: string
          username: string | null
          description: string | null
          member_count: number | null
          active: boolean
          last_signal: string | null
          signal_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          username?: string | null
          description?: string | null
          member_count?: number | null
          active?: boolean
          last_signal?: string | null
          signal_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          username?: string | null
          description?: string | null
          member_count?: number | null
          active?: boolean
          last_signal?: string | null
          signal_count?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      telegram_signals: {
        Row: {
          id: string
          channel_id: string
          channel_name: string
          message_id: number
          content: string
          token_address: string | null
          token_symbol: string | null
          market_cap: number | null
          images: string[] | null
          timestamp: string
          processed: boolean
          valid: boolean
          error: string | null
          created_at: string
        }
        Insert: {
          id?: string
          channel_id: string
          channel_name: string
          message_id: number
          content: string
          token_address?: string | null
          token_symbol?: string | null
          market_cap?: number | null
          images?: string[] | null
          timestamp: string
          processed?: boolean
          valid?: boolean
          error?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          channel_id?: string
          channel_name?: string
          message_id?: number
          content?: string
          token_address?: string | null
          token_symbol?: string | null
          market_cap?: number | null
          images?: string[] | null
          timestamp?: string
          processed?: boolean
          valid?: boolean
          error?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "telegram_signals_channel_id_fkey"
            columns: ["channel_id"]
            referencedRelation: "telegram_channels"
            referencedColumns: ["id"]
          }
        ]
      }
      portfolios: {
        Row: {
          id: string
          user_id: string
          total_value: number
          total_pnl: number
          total_pnl_percentage: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          total_value?: number
          total_pnl?: number
          total_pnl_percentage?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          total_value?: number
          total_pnl?: number
          total_pnl_percentage?: number
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "portfolios_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      positions: {
        Row: {
          id: string
          portfolio_id: string
          token_address: string
          token_symbol: string
          token_name: string
          amount: number
          entry_price: number
          current_price: number
          entry_value: number
          current_value: number
          pnl: number
          pnl_percentage: number
          multiplier: number
          status: 'active' | 'closed'
          entry_date: string
          exit_date: string | null
          signal_id: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          portfolio_id: string
          token_address: string
          token_symbol: string
          token_name: string
          amount: number
          entry_price: number
          current_price: number
          entry_value: number
          current_value: number
          pnl: number
          pnl_percentage: number
          multiplier: number
          status?: 'active' | 'closed'
          entry_date: string
          exit_date?: string | null
          signal_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          portfolio_id?: string
          token_address?: string
          token_symbol?: string
          token_name?: string
          amount?: number
          entry_price?: number
          current_price?: number
          entry_value?: number
          current_value?: number
          pnl?: number
          pnl_percentage?: number
          multiplier?: number
          status?: 'active' | 'closed'
          entry_date?: string
          exit_date?: string | null
          signal_id?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "positions_portfolio_id_fkey"
            columns: ["portfolio_id"]
            referencedRelation: "portfolios"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "positions_signal_id_fkey"
            columns: ["signal_id"]
            referencedRelation: "telegram_signals"
            referencedColumns: ["id"]
          }
        ]
      }
      trades: {
        Row: {
          id: string
          portfolio_id: string
          position_id: string | null
          type: 'buy' | 'sell'
          token_address: string
          token_symbol: string
          amount: number
          price: number
          value: number
          slippage: number
          fees: number
          signature: string
          status: 'pending' | 'confirmed' | 'failed'
          timestamp: string
          signal_id: string | null
          created_at: string
        }
        Insert: {
          id?: string
          portfolio_id: string
          position_id?: string | null
          type: 'buy' | 'sell'
          token_address: string
          token_symbol: string
          amount: number
          price: number
          value: number
          slippage: number
          fees: number
          signature: string
          status?: 'pending' | 'confirmed' | 'failed'
          timestamp: string
          signal_id?: string | null
          created_at?: string
        }
        Update: {
          id?: string
          portfolio_id?: string
          position_id?: string | null
          type?: 'buy' | 'sell'
          token_address?: string
          token_symbol?: string
          amount?: number
          price?: number
          value?: number
          slippage?: number
          fees?: number
          signature?: string
          status?: 'pending' | 'confirmed' | 'failed'
          timestamp?: string
          signal_id?: string | null
          created_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "trades_portfolio_id_fkey"
            columns: ["portfolio_id"]
            referencedRelation: "portfolios"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "trades_position_id_fkey"
            columns: ["position_id"]
            referencedRelation: "positions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "trades_signal_id_fkey"
            columns: ["signal_id"]
            referencedRelation: "telegram_signals"
            referencedColumns: ["id"]
          }
        ]
      }
      support_tickets: {
        Row: {
          id: string
          user_id: string
          email: string
          category: 'technical' | 'trading' | 'account' | 'feature_request' | 'general'
          priority: 'low' | 'medium' | 'high' | 'critical'
          status: 'open' | 'in_progress' | 'resolved' | 'closed'
          title: string
          description: string
          ticket_number: string
          admin_notes: string | null
          resolved_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          email: string
          category: 'technical' | 'trading' | 'account' | 'feature_request' | 'general'
          priority: 'low' | 'medium' | 'high' | 'critical'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          title: string
          description: string
          ticket_number: string
          admin_notes?: string | null
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          email?: string
          category?: 'technical' | 'trading' | 'account' | 'feature_request' | 'general'
          priority?: 'low' | 'medium' | 'high' | 'critical'
          status?: 'open' | 'in_progress' | 'resolved' | 'closed'
          title?: string
          description?: string
          ticket_number?: string
          admin_notes?: string | null
          resolved_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "support_tickets_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
