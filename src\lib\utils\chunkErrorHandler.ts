/**
 * Global chunk loading error handler for Signal V1
 * Handles chunk loading failures and provides automatic recovery
 */

import React from 'react';
import { devLog } from './environment';

interface ChunkErrorEvent extends ErrorEvent {
  error: Error & {
    name?: string;
    message: string;
  };
}

/**
 * Initialize global chunk error handling
 * Should be called once in the application root
 */
export function initializeChunkErrorHandler() {
  if (typeof window === 'undefined') {
    return; // Server-side, do nothing
  }

  // Handle unhandled promise rejections (common with dynamic imports)
  window.addEventListener('unhandledrejection', (event) => {
    const error = event.reason;
    
    if (isChunkLoadingError(error)) {
      devLog.warn('Unhandled chunk loading rejection:', error);
      event.preventDefault(); // Prevent the error from being logged to console
      
      // Attempt recovery
      handleChunkError(error);
    }
  });

  // Handle regular errors
  window.addEventListener('error', (event: ChunkErrorEvent) => {
    const error = event.error;
    
    if (isChunkLoadingError(error)) {
      devLog.warn('Chunk loading error detected:', error);
      event.preventDefault(); // Prevent the error from being logged to console
      
      // Attempt recovery
      handleChunkError(error);
    }
  });

  // Handle resource loading errors (for CSS chunks, etc.)
  window.addEventListener('error', (event) => {
    const target = event.target as HTMLElement;
    
    if (target && (target.tagName === 'LINK' || target.tagName === 'SCRIPT')) {
      const src = (target as any).src || (target as any).href;
      
      if (src && src.includes('chunk')) {
        devLog.warn('Resource chunk loading failed:', src);
        
        // Attempt to reload the resource
        setTimeout(() => {
          if (target.tagName === 'SCRIPT') {
            reloadScript(src);
          } else if (target.tagName === 'LINK') {
            reloadStylesheet(src);
          }
        }, 1000);
      }
    }
  }, true); // Use capture phase to catch resource errors
}

/**
 * Check if an error is related to chunk loading
 */
function isChunkLoadingError(error: any): boolean {
  if (!error) return false;
  
  const message = error.message || '';
  const name = error.name || '';
  
  return (
    message.includes('Loading chunk') ||
    message.includes('Failed to import') ||
    message.includes('Loading CSS chunk') ||
    message.includes('ChunkLoadError') ||
    message.includes('Failed to fetch') ||
    message.includes('Failed to load chunk') ||
    name === 'ChunkLoadError' ||
    // Dynamic import failures
    message.includes('Cannot resolve module') ||
    message.includes('Module not found')
  );
}

/**
 * Handle chunk loading errors with recovery strategies
 */
function handleChunkError(error: any) {
  devLog.error('Handling chunk error:', error);
  
  // Strategy 1: Clear any cached chunks and reload
  if ('caches' in window) {
    caches.keys().then(cacheNames => {
      cacheNames.forEach(cacheName => {
        if (cacheName.includes('chunk') || cacheName.includes('static')) {
          caches.delete(cacheName);
        }
      });
    });
  }
  
  // Strategy 2: Reload the page after a short delay
  setTimeout(() => {
    devLog.log('Reloading page due to chunk error...');
    window.location.reload();
  }, 2000);
}

/**
 * Attempt to reload a failed script
 */
function reloadScript(src: string) {
  const existingScript = document.querySelector(`script[src="${src}"]`);
  if (existingScript) {
    existingScript.remove();
  }
  
  const newScript = document.createElement('script');
  newScript.src = src + '?retry=' + Date.now();
  newScript.async = true;
  
  newScript.onload = () => {
    devLog.log('Successfully reloaded script:', src);
  };
  
  newScript.onerror = () => {
    devLog.error('Failed to reload script:', src);
    // If script reload fails, reload the entire page
    setTimeout(() => window.location.reload(), 1000);
  };
  
  document.head.appendChild(newScript);
}

/**
 * Attempt to reload a failed stylesheet
 */
function reloadStylesheet(href: string) {
  const existingLink = document.querySelector(`link[href="${href}"]`);
  if (existingLink) {
    existingLink.remove();
  }
  
  const newLink = document.createElement('link');
  newLink.rel = 'stylesheet';
  newLink.href = href + '?retry=' + Date.now();
  
  newLink.onload = () => {
    devLog.log('Successfully reloaded stylesheet:', href);
  };
  
  newLink.onerror = () => {
    devLog.error('Failed to reload stylesheet:', href);
  };
  
  document.head.appendChild(newLink);
}

/**
 * React hook to initialize chunk error handling
 */
export function useChunkErrorHandler() {
  React.useEffect(() => {
    if (typeof window !== 'undefined') {
      initializeChunkErrorHandler();
    }
  }, []);
}

// Auto-initialize if this module is imported
if (typeof window !== 'undefined') {
  // Delay initialization to ensure DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeChunkErrorHandler);
  } else {
    initializeChunkErrorHandler();
  }
}
