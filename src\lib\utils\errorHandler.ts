// Error Handling and Logging Utilities

export enum ErrorType {
  VALIDATION = 'VALIDATION',
  NETWORK = 'NETWORK',
  WALLET = 'WALLET',
  TRADING = 'TRADING',
  SIGNAL = 'SIGNAL',
  DATABASE = 'DATABASE',
  AUTHENTICATION = 'AUTHENTICATION',
  RATE_LIMIT = 'RATE_LIMIT',
  UNKNOWN = 'UNKNOWN',
}

export enum ErrorSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface AppError {
  id: string;
  type: ErrorType;
  severity: ErrorSeverity;
  message: string;
  details?: any;
  timestamp: Date;
  userId?: string;
  context?: Record<string, any>;
  stack?: string;
}

export class ErrorHandler {
  private static errors: AppError[] = [];
  private static maxErrors = 100;

  /**
   * Create and log an error
   */
  static createError(
    type: ErrorType,
    severity: ErrorSeverity,
    message: string,
    details?: any,
    context?: Record<string, any>
  ): AppError {
    const error: AppError = {
      id: this.generateErrorId(),
      type,
      severity,
      message,
      details,
      timestamp: new Date(),
      context,
      stack: new Error().stack,
    };

    this.logError(error);
    return error;
  }

  /**
   * Log error to console and storage
   */
  private static logError(error: AppError): void {
    // Add to in-memory storage
    this.errors.unshift(error);
    if (this.errors.length > this.maxErrors) {
      this.errors = this.errors.slice(0, this.maxErrors);
    }

    // Log to console based on severity
    const logMessage = `[${error.type}] ${error.message}`;
    
    switch (error.severity) {
      case ErrorSeverity.CRITICAL:
        console.error('🚨 CRITICAL:', logMessage, error);
        break;
      case ErrorSeverity.HIGH:
        console.error('❌ HIGH:', logMessage, error);
        break;
      case ErrorSeverity.MEDIUM:
        console.warn('⚠️ MEDIUM:', logMessage, error);
        break;
      case ErrorSeverity.LOW:
        console.log('ℹ️ LOW:', logMessage, error);
        break;
    }

    // Store in localStorage for persistence
    try {
      const storedErrors = JSON.parse(localStorage.getItem('app_errors') || '[]');
      storedErrors.unshift({
        ...error,
        timestamp: error.timestamp.toISOString(),
      });
      
      // Keep only last 50 errors in localStorage
      const trimmedErrors = storedErrors.slice(0, 50);
      localStorage.setItem('app_errors', JSON.stringify(trimmedErrors));
    } catch (e) {
      console.error('Failed to store error in localStorage:', e);
    }

    // Send to external logging service in production
    if (process.env.NODE_ENV === 'production') {
      this.sendToLoggingService(error);
    }
  }

  /**
   * Send error to external logging service
   */
  private static async sendToLoggingService(error: AppError): Promise<void> {
    try {
      // This would integrate with services like Sentry, LogRocket, etc.
      // For now, we'll just log it
      console.log('Would send to logging service:', error);
    } catch (e) {
      console.error('Failed to send error to logging service:', e);
    }
  }

  /**
   * Generate unique error ID
   */
  private static generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Get recent errors
   */
  static getRecentErrors(limit: number = 20): AppError[] {
    return this.errors.slice(0, limit);
  }

  /**
   * Get errors by type
   */
  static getErrorsByType(type: ErrorType): AppError[] {
    return this.errors.filter(error => error.type === type);
  }

  /**
   * Get errors by severity
   */
  static getErrorsBySeverity(severity: ErrorSeverity): AppError[] {
    return this.errors.filter(error => error.severity === severity);
  }

  /**
   * Clear all errors
   */
  static clearErrors(): void {
    this.errors = [];
    localStorage.removeItem('app_errors');
  }

  /**
   * Handle wallet errors
   */
  static handleWalletError(error: any, context?: Record<string, any>): AppError {
    let message = 'Wallet operation failed';
    let severity = ErrorSeverity.MEDIUM;

    if (error?.message) {
      if (error.message.includes('User rejected')) {
        message = 'Transaction was rejected by user';
        severity = ErrorSeverity.LOW;
      } else if (error.message.includes('insufficient funds')) {
        message = 'Insufficient funds for transaction';
        severity = ErrorSeverity.MEDIUM;
      } else if (error.message.includes('network')) {
        message = 'Network connection error';
        severity = ErrorSeverity.HIGH;
      } else {
        message = error.message;
      }
    }

    return this.createError(ErrorType.WALLET, severity, message, error, context);
  }

  /**
   * Handle trading errors
   */
  static handleTradingError(error: any, context?: Record<string, any>): AppError {
    let message = 'Trading operation failed';
    let severity = ErrorSeverity.HIGH;

    if (error?.message) {
      if (error.message.includes('slippage')) {
        message = 'Trade failed due to slippage tolerance';
        severity = ErrorSeverity.MEDIUM;
      } else if (error.message.includes('liquidity')) {
        message = 'Insufficient liquidity for trade';
        severity = ErrorSeverity.MEDIUM;
      } else if (error.message.includes('balance')) {
        message = 'Insufficient balance for trade';
        severity = ErrorSeverity.MEDIUM;
      } else {
        message = error.message;
      }
    }

    return this.createError(ErrorType.TRADING, severity, message, error, context);
  }

  /**
   * Handle network errors
   */
  static handleNetworkError(error: any, context?: Record<string, any>): AppError {
    let message = 'Network request failed';
    let severity = ErrorSeverity.MEDIUM;

    if (error?.status) {
      switch (error.status) {
        case 429:
          message = 'Rate limit exceeded';
          severity = ErrorSeverity.LOW;
          break;
        case 500:
          message = 'Server error';
          severity = ErrorSeverity.HIGH;
          break;
        case 503:
          message = 'Service unavailable';
          severity = ErrorSeverity.HIGH;
          break;
        default:
          message = `Network error (${error.status})`;
      }
    }

    return this.createError(ErrorType.NETWORK, severity, message, error, context);
  }

  /**
   * Handle validation errors
   */
  static handleValidationError(message: string, details?: any): AppError {
    return this.createError(ErrorType.VALIDATION, ErrorSeverity.LOW, message, details);
  }

  /**
   * Handle signal processing errors
   */
  static handleSignalError(error: any, context?: Record<string, any>): AppError {
    const message = error?.message || 'Signal processing failed';
    return this.createError(ErrorType.SIGNAL, ErrorSeverity.MEDIUM, message, error, context);
  }

  /**
   * Handle database errors
   */
  static handleDatabaseError(error: any, context?: Record<string, any>): AppError {
    const message = error?.message || 'Database operation failed';
    const severity = error?.code === 'PGRST116' ? ErrorSeverity.LOW : ErrorSeverity.HIGH;
    return this.createError(ErrorType.DATABASE, severity, message, error, context);
  }

  /**
   * Get error statistics
   */
  static getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    bySeverity: Record<ErrorSeverity, number>;
    recent24h: number;
  } {
    const now = Date.now();
    const oneDayAgo = now - 24 * 60 * 60 * 1000;

    const byType = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = this.errors.filter(e => e.type === type).length;
      return acc;
    }, {} as Record<ErrorType, number>);

    const bySeverity = Object.values(ErrorSeverity).reduce((acc, severity) => {
      acc[severity] = this.errors.filter(e => e.severity === severity).length;
      return acc;
    }, {} as Record<ErrorSeverity, number>);

    const recent24h = this.errors.filter(e => e.timestamp.getTime() > oneDayAgo).length;

    return {
      total: this.errors.length,
      byType,
      bySeverity,
      recent24h,
    };
  }
}

/**
 * Global error boundary for React components
 */
export function withErrorBoundary<T extends Record<string, any>>(
  Component: React.ComponentType<T>
): React.ComponentType<T> {
  return class ErrorBoundaryWrapper extends React.Component<T, { hasError: boolean; error?: Error }> {
    constructor(props: T) {
      super(props);
      this.state = { hasError: false };
    }

    static getDerivedStateFromError(error: Error) {
      return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
      ErrorHandler.createError(
        ErrorType.UNKNOWN,
        ErrorSeverity.HIGH,
        'React component error',
        { error: error.message, errorInfo },
        { component: Component.name }
      );
    }

    render() {
      if (this.state.hasError) {
        return (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <h3 className="text-lg font-medium text-red-800 mb-2">Something went wrong</h3>
            <p className="text-red-600">
              An error occurred while rendering this component. Please refresh the page or try again later.
            </p>
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-2">
                <summary className="cursor-pointer text-sm text-red-700">Error details</summary>
                <pre className="mt-2 text-xs text-red-600 overflow-auto">
                  {this.state.error.stack}
                </pre>
              </details>
            )}
          </div>
        );
      }

      return <Component {...this.props} />;
    }
  };
}
