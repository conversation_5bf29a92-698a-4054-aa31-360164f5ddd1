// Jupiter Trading Engine - Handles automated trading via Jupiter DEX

import { Connection, PublicKey, Transaction, VersionedTransaction } from '@solana/web3.js';
import { JupiterAPI } from '../jupiter/api';
import { SOLANA_CONFIG, TRADING_CONFIG, JUPITER_CONFIG } from '../constants';
import { PortfolioService } from '../supabase/services/portfolioService';
import { solToLamports, lamportsToSol } from '../utils';
import type { Trade, Position } from '@/types';

export interface TradeParams {
  tokenAddress: string;
  tokenSymbol: string;
  amount: number; // in SOL
  slippageBps?: number;
  signalId?: string;
}

export interface TradeResult {
  success: boolean;
  signature?: string;
  error?: string;
  trade?: Trade;
  position?: Position;
}

export class JupiterTrader {
  private connection: Connection;
  private wallet: any; // Wallet adapter instance

  constructor(wallet: any) {
    this.connection = new Connection(SOLANA_CONFIG.rpcUrl, SOLANA_CONFIG.commitment);
    this.wallet = wallet;
  }

  /**
   * Execute a buy trade
   */
  async buyToken(params: TradeParams): Promise<TradeResult> {
    try {
      if (!this.wallet.connected || !this.wallet.publicKey) {
        return { success: false, error: 'Wallet not connected' };
      }

      // Validate trade parameters
      const validation = this.validateTradeParams(params);
      if (!validation.valid) {
        return { success: false, error: validation.error };
      }

      // Check wallet balance
      const balance = await this.getWalletBalance();
      if (balance < params.amount) {
        return { success: false, error: 'Insufficient SOL balance' };
      }

      // Check position limits
      const canTrade = await this.checkPositionLimits(params.amount);
      if (!canTrade.allowed) {
        return { success: false, error: canTrade.reason };
      }

      // Get quote from Jupiter
      const quote = await this.getQuote(
        'So11111111111111111111111111111111111111112', // SOL
        params.tokenAddress,
        solToLamports(params.amount),
        params.slippageBps || JUPITER_CONFIG.slippageBps
      );

      if (!quote) {
        return { success: false, error: 'Failed to get quote from Jupiter' };
      }

      // Get swap transaction
      const swapTransaction = await JupiterAPI.getSwapTransaction(
        quote,
        this.wallet.publicKey.toString(),
        true
      );

      if (!swapTransaction) {
        return { success: false, error: 'Failed to get swap transaction' };
      }

      // Execute the transaction
      const signature = await this.executeTransaction(swapTransaction.swapTransaction);
      
      if (!signature) {
        return { success: false, error: 'Transaction failed to execute' };
      }

      // Calculate trade details
      const outputAmount = parseFloat(quote.outAmount) / Math.pow(10, 6); // Assuming 6 decimals
      const price = params.amount / outputAmount;
      const fees = this.calculateFees(quote);

      // Create trade record
      const trade: Omit<Trade, 'id' | 'createdAt'> = {
        portfolioId: '', // Will be set by service
        positionId: undefined,
        type: 'buy',
        tokenAddress: params.tokenAddress,
        tokenSymbol: params.tokenSymbol,
        amount: outputAmount,
        price,
        value: params.amount,
        slippage: params.slippageBps || JUPITER_CONFIG.slippageBps,
        fees,
        signature,
        status: 'confirmed',
        timestamp: new Date(),
        signalId: params.signalId,
      };

      // Record trade in database
      const savedTrade = await PortfolioService.recordTrade(trade);

      // Create or update position
      const position = await this.createOrUpdatePosition({
        tokenAddress: params.tokenAddress,
        tokenSymbol: params.tokenSymbol,
        amount: outputAmount,
        entryPrice: price,
        entryValue: params.amount,
        signalId: params.signalId,
      });

      return {
        success: true,
        signature,
        trade: savedTrade || undefined,
        position,
      };
    } catch (error) {
      console.error('Error executing buy trade:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Execute a sell trade
   */
  async sellToken(params: TradeParams & { percentage?: number }): Promise<TradeResult> {
    try {
      if (!this.wallet.connected || !this.wallet.publicKey) {
        return { success: false, error: 'Wallet not connected' };
      }

      // Find existing position
      const positions = await PortfolioService.getActivePositions();
      const position = positions.find(p => p.tokenAddress === params.tokenAddress);

      if (!position) {
        return { success: false, error: 'No active position found for this token' };
      }

      // Calculate amount to sell
      const sellPercentage = params.percentage || 100;
      const sellAmount = (position.amount * sellPercentage) / 100;

      if (sellAmount <= 0) {
        return { success: false, error: 'Invalid sell amount' };
      }

      // Get quote from Jupiter
      const quote = await this.getQuote(
        params.tokenAddress,
        'So11111111111111111111111111111111111111112', // SOL
        Math.floor(sellAmount * Math.pow(10, 6)), // Assuming 6 decimals
        params.slippageBps || JUPITER_CONFIG.slippageBps
      );

      if (!quote) {
        return { success: false, error: 'Failed to get quote from Jupiter' };
      }

      // Get swap transaction
      const swapTransaction = await JupiterAPI.getSwapTransaction(
        quote,
        this.wallet.publicKey.toString(),
        true
      );

      if (!swapTransaction) {
        return { success: false, error: 'Failed to get swap transaction' };
      }

      // Execute the transaction
      const signature = await this.executeTransaction(swapTransaction.swapTransaction);
      
      if (!signature) {
        return { success: false, error: 'Transaction failed to execute' };
      }

      // Calculate trade details
      const outputSOL = lamportsToSol(parseFloat(quote.outAmount));
      const price = outputSOL / sellAmount;
      const fees = this.calculateFees(quote);

      // Create trade record
      const trade: Omit<Trade, 'id' | 'createdAt'> = {
        portfolioId: '', // Will be set by service
        positionId: position.id,
        type: 'sell',
        tokenAddress: params.tokenAddress,
        tokenSymbol: params.tokenSymbol,
        amount: sellAmount,
        price,
        value: outputSOL,
        slippage: params.slippageBps || JUPITER_CONFIG.slippageBps,
        fees,
        signature,
        status: 'confirmed',
        timestamp: new Date(),
        signalId: params.signalId,
      };

      // Record trade in database
      const savedTrade = await PortfolioService.recordTrade(trade);

      // Update position
      const updatedPosition = await this.updatePositionAfterSell(position, sellAmount, sellPercentage);

      return {
        success: true,
        signature,
        trade: savedTrade || undefined,
        position: updatedPosition,
      };
    } catch (error) {
      console.error('Error executing sell trade:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  /**
   * Get quote from Jupiter
   */
  private async getQuote(
    inputMint: string,
    outputMint: string,
    amount: number,
    slippageBps: number
  ): Promise<any> {
    return await JupiterAPI.getQuote(inputMint, outputMint, amount, slippageBps);
  }

  /**
   * Execute transaction
   */
  private async executeTransaction(transactionData: string): Promise<string | null> {
    try {
      // Deserialize the transaction
      const transaction = VersionedTransaction.deserialize(Buffer.from(transactionData, 'base64'));
      
      // Sign the transaction
      const signedTransaction = await this.wallet.signTransaction(transaction);
      
      // Send the transaction
      const signature = await this.connection.sendRawTransaction(signedTransaction.serialize(), {
        skipPreflight: false,
        preflightCommitment: 'confirmed',
      });

      // Confirm the transaction
      const confirmation = await this.connection.confirmTransaction(signature, 'confirmed');
      
      if (confirmation.value.err) {
        console.error('Transaction failed:', confirmation.value.err);
        return null;
      }

      return signature;
    } catch (error) {
      console.error('Error executing transaction:', error);
      return null;
    }
  }

  /**
   * Get wallet SOL balance
   */
  private async getWalletBalance(): Promise<number> {
    try {
      if (!this.wallet.publicKey) return 0;
      
      const balance = await this.connection.getBalance(this.wallet.publicKey);
      return lamportsToSol(balance);
    } catch (error) {
      console.error('Error getting wallet balance:', error);
      return 0;
    }
  }

  /**
   * Validate trade parameters
   */
  private validateTradeParams(params: TradeParams): { valid: boolean; error?: string } {
    if (!params.tokenAddress || params.tokenAddress.length !== 44) {
      return { valid: false, error: 'Invalid token address' };
    }

    if (!params.amount || params.amount <= 0) {
      return { valid: false, error: 'Invalid trade amount' };
    }

    if (params.amount < 0.001) {
      return { valid: false, error: 'Trade amount too small (minimum 0.001 SOL)' };
    }

    if (params.amount > 100) {
      return { valid: false, error: 'Trade amount too large (maximum 100 SOL)' };
    }

    return { valid: true };
  }

  /**
   * Check position limits
   */
  private async checkPositionLimits(amount: number): Promise<{ allowed: boolean; reason?: string }> {
    try {
      const activePositions = await PortfolioService.getActivePositions();
      
      // Check maximum active positions
      if (activePositions.length >= TRADING_CONFIG.maxActivePositions) {
        return { allowed: false, reason: 'Maximum active positions reached' };
      }

      // Check position size limit (percentage of portfolio)
      const portfolio = await PortfolioService.getUserPortfolio();
      if (portfolio && portfolio.totalValue > 0) {
        const positionPercentage = (amount / portfolio.totalValue) * 100;
        if (positionPercentage > TRADING_CONFIG.maxPositionPercentage) {
          return { allowed: false, reason: 'Position size exceeds maximum percentage' };
        }
      }

      return { allowed: true };
    } catch (error) {
      console.error('Error checking position limits:', error);
      return { allowed: false, reason: 'Failed to check position limits' };
    }
  }

  /**
   * Calculate fees from quote
   */
  private calculateFees(quote: any): number {
    // Jupiter fees are typically included in the price impact
    // This is a simplified calculation
    const platformFee = quote.platformFee ? parseFloat(quote.platformFee.amount) : 0;
    const priceImpact = parseFloat(quote.priceImpactPct) || 0;
    
    return platformFee + (priceImpact * 0.01); // Convert percentage to decimal
  }

  /**
   * Create or update position
   */
  private async createOrUpdatePosition(params: {
    tokenAddress: string;
    tokenSymbol: string;
    amount: number;
    entryPrice: number;
    entryValue: number;
    signalId?: string;
  }): Promise<Position | null> {
    try {
      const position: Omit<Position, 'id' | 'createdAt' | 'updatedAt'> = {
        portfolioId: '', // Will be set by service
        tokenAddress: params.tokenAddress,
        tokenSymbol: params.tokenSymbol,
        tokenName: params.tokenSymbol, // Token name will be fetched from metadata service
        amount: params.amount,
        entryPrice: params.entryPrice,
        currentPrice: params.entryPrice,
        entryValue: params.entryValue,
        currentValue: params.entryValue,
        pnl: 0,
        pnlPercentage: 0,
        multiplier: 1,
        status: 'active',
        entryDate: new Date(),
        signalId: params.signalId,
      };

      return await PortfolioService.createPosition(position);
    } catch (error) {
      console.error('Error creating position:', error);
      return null;
    }
  }

  /**
   * Update position after sell
   */
  private async updatePositionAfterSell(
    position: Position,
    sellAmount: number,
    sellPercentage: number
  ): Promise<Position | null> {
    try {
      if (sellPercentage >= 100) {
        // Close position completely
        await PortfolioService.updatePosition(position.id, {
          status: 'closed',
          exitDate: new Date(),
        });
      } else {
        // Reduce position size
        const newAmount = position.amount - sellAmount;
        const newEntryValue = position.entryValue * (newAmount / position.amount);
        
        await PortfolioService.updatePosition(position.id, {
          amount: newAmount,
          entryValue: newEntryValue,
        });
      }

      // Return updated position (simplified)
      return {
        ...position,
        amount: sellPercentage >= 100 ? 0 : position.amount - sellAmount,
        status: sellPercentage >= 100 ? 'closed' : 'active',
        exitDate: sellPercentage >= 100 ? new Date() : undefined,
      };
    } catch (error) {
      console.error('Error updating position after sell:', error);
      return null;
    }
  }
}
