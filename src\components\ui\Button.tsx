'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'outline' | 'ghost' | 'destructive' | 'ape' | 'moon' | 'rekt' | 'diamond';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  children: React.ReactNode;
}

export function Button({
  className,
  variant = 'default',
  size = 'md',
  loading = false,
  disabled,
  children,
  ...props
}: ButtonProps) {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none relative overflow-hidden btn-enhanced transform-gpu';

  const variants = {
    default: 'bg-neutral-700 text-white hover:bg-neutral-600 focus:ring-2 focus:ring-neutral-500 shadow-sm hover:shadow-md',
    outline: 'border border-neutral-600 bg-transparent text-neutral-300 hover:bg-neutral-800 focus:ring-2 focus:ring-neutral-500 hover:border-neutral-500',
    ghost: 'text-neutral-300 hover:bg-neutral-800 focus:ring-2 focus:ring-neutral-500',
    destructive: 'bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 shadow-sm hover:shadow-md',
    ape: 'bg-green-600 text-white hover:bg-green-700 focus:ring-2 focus:ring-green-500 shadow-sm font-bold hover:shadow-md',
    moon: 'bg-neutral-600 text-white hover:bg-neutral-500 focus:ring-2 focus:ring-neutral-400 shadow-sm font-bold hover:shadow-md',
    rekt: 'bg-red-600 text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 shadow-sm hover:shadow-md',
    diamond: 'bg-neutral-700 text-white hover:bg-neutral-600 focus:ring-2 focus:ring-neutral-500 shadow-sm font-bold hover:shadow-md',
  };

  const sizes = {
    sm: 'px-3 py-1.5 text-sm rounded-md',
    md: 'px-4 py-2 text-sm rounded-lg',
    lg: 'px-6 py-3 text-base rounded-lg',
  };

  return (
    <button
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <div className="flex items-center mr-2">
          <svg
            className="animate-spin h-4 w-4"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
          <div className="ml-1 w-1 h-1 bg-current rounded-full animate-pulse" />
          <div className="ml-1 w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.2s' }} />
          <div className="ml-1 w-1 h-1 bg-current rounded-full animate-pulse" style={{ animationDelay: '0.4s' }} />
        </div>
      )}
      {children}
    </button>
  );
}
