'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  isHMRError: boolean;
  retryCount: number;
}

class HMRErrorBoundary extends Component<Props, State> {
  private retryTimeout: NodeJS.Timeout | null = null;
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      isHMRError: false,
      retryCount: 0,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // Check if this is an HMR module factory error
    const isHMRError = error.message.includes('module factory') ||
                      error.message.includes('was instantiated because it was required') ||
                      error.message.includes('but the module factory is not available') ||
                      error.message.includes('HMR') ||
                      error.message.includes('Cannot access before initialization');

    // Check for chunk loading errors
    const isChunkError = error.message.includes('Loading chunk') ||
                        error.message.includes('Failed to import') ||
                        error.message.includes('Loading CSS chunk') ||
                        error.message.includes('ChunkLoadError') ||
                        error.name === 'ChunkLoadError' ||
                        error.message.includes('Failed to fetch') ||
                        error.message.includes('Failed to load chunk');

    return {
      hasError: true,
      error,
      isHMRError: isHMRError || isChunkError,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo,
    });

    // Log HMR errors differently
    if (this.state.isHMRError) {
      console.warn('🔄 HMR Module Factory Error caught by boundary:', error.message);
      console.warn('🔄 This is likely due to Next.js 15 + Turbopack HMR instability');
      
      // Auto-retry for HMR errors
      if (this.state.retryCount < this.maxRetries) {
        console.log(`🔄 Auto-retrying in 2 seconds (attempt ${this.state.retryCount + 1}/${this.maxRetries})...`);
        this.retryTimeout = setTimeout(() => {
          this.handleRetry();
        }, 2000);
      }
    } else {
      console.error('❌ Application Error caught by boundary:', error);
      console.error('❌ Error Info:', errorInfo);
    }
  }

  componentWillUnmount() {
    if (this.retryTimeout) {
      clearTimeout(this.retryTimeout);
    }
  }

  handleRetry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      isHMRError: false,
      retryCount: prevState.retryCount + 1,
    }));
  };

  handleManualRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      isHMRError: false,
      retryCount: 0,
    });
  };

  handleRefresh = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      const { error, isHMRError, retryCount } = this.state;

      if (isHMRError) {
        return (
          <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
            <div className="bg-gray-800 border border-gray-700 rounded-2xl p-8 max-w-md w-full text-center">
              <div className="text-6xl mb-4">🔄</div>
              <h2 className="text-xl font-bold text-white mb-4">
                {this.state.error?.message.includes('chunk') || this.state.error?.message.includes('Failed to fetch')
                  ? 'Chunk Loading Issue'
                  : 'HMR Module Loading Issue'}
              </h2>
              <p className="text-gray-300 mb-6 text-sm leading-relaxed">
                {this.state.error?.message.includes('chunk') || this.state.error?.message.includes('Failed to fetch')
                  ? 'Failed to load application resources. This usually happens when the app is updated while you\'re using it.'
                  : 'Next.js 15 + Turbopack is experiencing module factory instability. This is a known development issue and will resolve automatically.'}
              </p>
              
              {retryCount < this.maxRetries ? (
                <div className="space-y-4">
                  <div className="text-green-400 text-sm">
                    Auto-retrying... (attempt {retryCount + 1}/{this.maxRetries})
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-2000 ease-out"
                      style={{ width: '100%', animation: 'progress 2s linear' }}
                    />
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <p className="text-yellow-400 text-sm mb-4">
                    Auto-retry limit reached. Manual action required.
                  </p>
                  <div className="flex gap-3">
                    <button
                      onClick={this.handleManualRetry}
                      className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      Retry
                    </button>
                    <button
                      onClick={this.handleRefresh}
                      className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                    >
                      Refresh Page
                    </button>
                  </div>
                </div>
              )}

              <details className="mt-6 text-left">
                <summary className="text-gray-400 text-xs cursor-pointer hover:text-gray-300">
                  Technical Details
                </summary>
                <div className="mt-2 p-3 bg-gray-900 rounded-lg text-xs text-gray-400 font-mono break-all">
                  {error?.message}
                </div>
              </details>
            </div>

            <style jsx>{`
              @keyframes progress {
                from { width: 0%; }
                to { width: 100%; }
              }
            `}</style>
          </div>
        );
      }

      // Regular error fallback
      return (
        <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
          <div className="bg-gray-800 border border-red-500/50 rounded-2xl p-8 max-w-md w-full text-center">
            <div className="text-6xl mb-4">❌</div>
            <h2 className="text-xl font-bold text-white mb-4">
              Something went wrong
            </h2>
            <p className="text-gray-300 mb-6 text-sm">
              An unexpected error occurred. Please try refreshing the page.
            </p>
            
            <div className="flex gap-3">
              <button
                onClick={this.handleManualRetry}
                className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Try Again
              </button>
              <button
                onClick={this.handleRefresh}
                className="flex-1 bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg font-medium transition-colors"
              >
                Refresh Page
              </button>
            </div>

            <details className="mt-6 text-left">
              <summary className="text-gray-400 text-xs cursor-pointer hover:text-gray-300">
                Error Details
              </summary>
              <div className="mt-2 p-3 bg-gray-900 rounded-lg text-xs text-gray-400 font-mono break-all">
                {error?.message}
              </div>
            </details>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default HMRErrorBoundary;
