// Unit Tests for Utility Functions


import {
  formatCurrency,
  formatPercentage,
  formatSOL,
  formatMultiplier,
  formatRelativeTime,
  isValidSolanaAddress,
  shortenAddress,
  calculatePercentageChange,
  calculateMultiplier,
  solToLamports,
  lamportsToSol,
  getChangeColor,
} from '../lib/utils';

describe('Currency Formatting', () => {
  it('should format USD currency correctly', () => {
    expect(formatCurrency(1234.56)).toBe('$1,234.56');
    expect(formatCurrency(0.123456, 'USD', 4)).toBe('$0.1235');
    expect(formatCurrency(1000000)).toBe('$1,000,000.00');
  });

  it('should handle edge cases', () => {
    expect(formatCurrency(0)).toBe('$0.00');
    expect(formatCurrency(-100)).toBe('-$100.00');
    expect(formatCurrency(0.001, 'USD', 6)).toBe('$0.001000');
  });
});

describe('Percentage Formatting', () => {
  it('should format percentages correctly', () => {
    expect(formatPercentage(25.5)).toBe('+25.50%');
    expect(formatPercentage(-10.25)).toBe('-10.25%');
    expect(formatPercentage(0)).toBe('0.00%');
  });

  it('should handle precision', () => {
    expect(formatPercentage(25.123456, 1)).toBe('+25.1%');
    expect(formatPercentage(25.123456, 4)).toBe('+25.1235%');
  });
});

describe('SOL Formatting', () => {
  it('should format SOL amounts correctly', () => {
    expect(formatSOL(1.5)).toBe('1.50 SOL');
    expect(formatSOL(0.001)).toBe('0.001 SOL');
    expect(formatSOL(1000)).toBe('1,000.00 SOL');
  });
});

describe('Multiplier Formatting', () => {
  it('should format multipliers correctly', () => {
    expect(formatMultiplier(2.5)).toBe('2.5x');
    expect(formatMultiplier(10)).toBe('10.0x');
    expect(formatMultiplier(0.5)).toBe('0.5x');
  });
});

describe('Solana Address Validation', () => {
  it('should validate correct Solana addresses', () => {
    const validAddress = 'So11111111111111111111111111111111111111112';
    expect(isValidSolanaAddress(validAddress)).toBe(true);
  });

  it('should reject invalid addresses', () => {
    expect(isValidSolanaAddress('')).toBe(false);
    expect(isValidSolanaAddress('invalid')).toBe(false);
    expect(isValidSolanaAddress('So1111111111111111111111111111111111111111')).toBe(false); // too short (42 chars)
    expect(isValidSolanaAddress('So1111111111111111111111111111111111111111234')).toBe(false); // too long (45 chars)
    expect(isValidSolanaAddress('So11111111111111111111111111111111111111!@')).toBe(false); // invalid chars
  });
});

describe('Address Shortening', () => {
  it('should shorten addresses correctly', () => {
    const address = 'So11111111111111111111111111111111111111112';
    expect(shortenAddress(address)).toBe('So111111...11111112');
    expect(shortenAddress(address, 6)).toBe('So1111...111112');
  });

  it('should handle short addresses', () => {
    expect(shortenAddress('short')).toBe('short');
    expect(shortenAddress('12345678')).toBe('12345678');
  });
});

describe('Percentage Change Calculation', () => {
  it('should calculate percentage change correctly', () => {
    expect(calculatePercentageChange(100, 150)).toBe(50);
    expect(calculatePercentageChange(100, 50)).toBe(-50);
    expect(calculatePercentageChange(100, 100)).toBe(0);
  });

  it('should handle edge cases', () => {
    expect(calculatePercentageChange(0, 100)).toBe(0);
    expect(calculatePercentageChange(100, 0)).toBe(-100);
  });
});

describe('Multiplier Calculation', () => {
  it('should calculate multipliers correctly', () => {
    expect(calculateMultiplier(100)).toBe(2);
    expect(calculateMultiplier(50)).toBe(1.5);
    expect(calculateMultiplier(-50)).toBe(0.5);
    expect(calculateMultiplier(0)).toBe(1);
  });
});

describe('SOL/Lamports Conversion', () => {
  it('should convert SOL to lamports correctly', () => {
    expect(solToLamports(1)).toBe(1000000000);
    expect(solToLamports(0.5)).toBe(500000000);
    expect(solToLamports(0.000000001)).toBe(1);
  });

  it('should convert lamports to SOL correctly', () => {
    expect(lamportsToSol(1000000000)).toBe(1);
    expect(lamportsToSol(500000000)).toBe(0.5);
    expect(lamportsToSol(1)).toBe(0.000000001);
  });

  it('should handle round-trip conversions', () => {
    const originalSOL = 1.5;
    const lamports = solToLamports(originalSOL);
    const convertedSOL = lamportsToSol(lamports);
    expect(convertedSOL).toBeCloseTo(originalSOL, 9);
  });
});

describe('Change Color Utility', () => {
  it('should return correct colors for changes', () => {
    expect(getChangeColor(10)).toContain('green');
    expect(getChangeColor(-10)).toContain('red');
    expect(getChangeColor(0)).toContain('gray');
  });
});

describe('Relative Time Formatting', () => {
  beforeEach(() => {
    // Mock Date.now to return a fixed timestamp
    jest.spyOn(Date, 'now').mockReturnValue(new Date('2024-01-01T12:00:00Z').getTime());
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  it('should format recent times correctly', () => {
    const now = new Date('2024-01-01T12:00:00Z');
    const oneMinuteAgo = new Date('2024-01-01T11:59:00Z');
    const oneHourAgo = new Date('2024-01-01T11:00:00Z');
    const oneDayAgo = new Date('2023-12-31T12:00:00Z');

    expect(formatRelativeTime(oneMinuteAgo, now)).toBe('1 minute ago');
    expect(formatRelativeTime(oneHourAgo, now)).toBe('1 hour ago');
    expect(formatRelativeTime(oneDayAgo, now)).toBe('1 day ago');
  });

  it('should handle future dates', () => {
    const now = new Date('2024-01-01T12:00:00Z');
    const future = new Date('2024-01-01T13:00:00Z');
    expect(formatRelativeTime(future, now)).toBe('in 1 hour');
  });
});

// Mock implementations for testing
jest.mock('../lib/constants', () => ({
  VALIDATION_RULES: {
    solanaAddress: {
      length: 44,
      pattern: /^[1-9A-HJ-NP-Za-km-z]{44}$/,
    },
  },
}));
