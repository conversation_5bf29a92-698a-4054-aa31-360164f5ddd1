'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { WalletButton } from '@/components/wallet/WalletButton';
import { Button } from '@/components/ui/Button';
import { PulseDot, ShimmerText } from '@/components/ui/LiveIndicator';
import { APP_CONFIG } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface HeaderProps {
  className?: string;
}

export function Header({ className }: HeaderProps) {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();

  const navigation = [
    { name: 'Command Center', href: '/', icon: '📊' },
    { name: 'Live Alpha', href: '/signals', icon: '📡' },
    { name: 'My Bag', href: '/portfolio', icon: '💼' },
    { name: 'Diamond Hands', href: '/trading', icon: '🤖' },
    { name: 'Settings', href: '/settings', icon: '⚙️' },
  ];

  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <header className={cn(
      'sticky top-0 z-40 w-full border-b border-gray-200 bg-white/80 backdrop-blur-sm',
      'dark:border-gray-700 dark:bg-gray-900/80',
      className
    )}>
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex h-16 items-center justify-between">
          {/* Logo and Brand */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 group">
              <div className="text-2xl breathe-effect">🚀</div>
              <ShimmerText>
                <span className="text-xl font-bold text-gray-900 dark:text-white">
                  {APP_CONFIG.name}
                </span>
              </ShimmerText>
              <PulseDot color="blue" size="sm" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {navigation.map((item, index) => (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors interactive-card',
                  isActive(item.href)
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400 glow-blue'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800'
                )}
                style={{ animationDelay: `${index * 50}ms` }}
                suppressHydrationWarning
              >
                <span className="breathe-effect">{item.icon}</span>
                <span>{item.name}</span>
                {isActive(item.href) && <PulseDot color="blue" size="sm" />}
              </Link>
            ))}
          </nav>

          {/* Right side actions */}
          <div className="flex items-center space-x-4">
            {/* Wallet Connection */}
            <WalletButton showBalance className="hidden sm:flex" />
            
            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            >
              <svg
                className="h-6 w-6"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                {isMobileMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden border-t border-gray-200 dark:border-gray-700 py-4">
            <nav className="space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className={cn(
                    'flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-colors',
                    isActive(item.href)
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-white dark:hover:bg-gray-800'
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                  suppressHydrationWarning
                >
                  <span className="text-xl">{item.icon}</span>
                  <span>{item.name}</span>
                </Link>
              ))}
            </nav>
            
            {/* Mobile wallet connection */}
            <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
              <WalletButton showBalance className="w-full" />
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
