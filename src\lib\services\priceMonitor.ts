// Price Monitoring Service - Tracks token prices and updates positions

import { PriceTracker } from '../jupiter/api';
import { PortfolioService } from '../supabase/services/portfolioService';
import { SignalService } from '../supabase/services/signalService';
import { calculatePercentageChange, calculateMultiplier } from '../utils';
import type { Position, TokenPrice, TelegramSignal } from '@/types';

export class PriceMonitorService {
  private static instance: PriceMonitorService;
  private isRunning = false;
  private intervalId: NodeJS.Timeout | null = null;
  private updateInterval = 30000; // 30 seconds
  private trackedTokens = new Set<string>();
  private priceHistory = new Map<string, TokenPrice[]>();

  private constructor() {}

  static getInstance(): PriceMonitorService {
    if (!PriceMonitorService.instance) {
      PriceMonitorService.instance = new PriceMonitorService();
    }
    return PriceMonitorService.instance;
  }

  /**
   * Start price monitoring
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('Price monitor is already running');
      return;
    }

    console.log('Starting price monitor...');
    this.isRunning = true;

    // Initial update
    await this.updatePrices();

    // Set up interval
    this.intervalId = setInterval(async () => {
      try {
        await this.updatePrices();
      } catch (error) {
        console.error('Error in price monitor interval:', error);
      }
    }, this.updateInterval);
  }

  /**
   * Stop price monitoring
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    console.log('Stopping price monitor...');
    this.isRunning = false;

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * Add token to tracking list
   */
  addToken(tokenAddress: string): void {
    this.trackedTokens.add(tokenAddress);
    console.log(`Added token to tracking: ${tokenAddress}`);
  }

  /**
   * Remove token from tracking list
   */
  removeToken(tokenAddress: string): void {
    this.trackedTokens.delete(tokenAddress);
    this.priceHistory.delete(tokenAddress);
    console.log(`Removed token from tracking: ${tokenAddress}`);
  }

  /**
   * Get tracked tokens
   */
  getTrackedTokens(): string[] {
    return Array.from(this.trackedTokens);
  }

  /**
   * Update prices for all tracked tokens
   */
  private async updatePrices(): Promise<void> {
    try {
      // Get active positions to track
      const activePositions = await PortfolioService.getActivePositions();
      
      // Get recent signals to track
      const recentSignals = await SignalService.getRecentSignals(50);
      
      // Combine all tokens to track
      const tokensToTrack = new Set<string>();
      
      // Add tokens from active positions
      activePositions.forEach(position => {
        tokensToTrack.add(position.tokenAddress);
      });
      
      // Add tokens from recent valid signals
      recentSignals
        .filter(signal => signal.valid && signal.tokenAddress)
        .forEach(signal => {
          tokensToTrack.add(signal.tokenAddress!);
        });
      
      // Add manually tracked tokens
      this.trackedTokens.forEach(token => {
        tokensToTrack.add(token);
      });

      if (tokensToTrack.size === 0) {
        return;
      }

      console.log(`Updating prices for ${tokensToTrack.size} tokens...`);

      // Get price data for all tokens
      const tokenAddresses = Array.from(tokensToTrack);
      const priceData = await PriceTracker.getMultipleTokenPriceData(tokenAddresses);

      // Update price history
      priceData.forEach(price => {
        this.updatePriceHistory(price);
      });

      // Update active positions
      await this.updatePositions(activePositions, priceData);

      // Check for significant price movements
      await this.checkPriceAlerts(priceData, recentSignals);

      console.log(`Updated prices for ${priceData.length} tokens`);
    } catch (error) {
      console.error('Error updating prices:', error);
    }
  }

  /**
   * Update price history for a token
   */
  private updatePriceHistory(priceData: TokenPrice): void {
    const history = this.priceHistory.get(priceData.address) || [];
    
    // Add new price data
    history.push(priceData);
    
    // Keep only last 100 entries (about 50 minutes of data at 30s intervals)
    if (history.length > 100) {
      history.shift();
    }
    
    this.priceHistory.set(priceData.address, history);
  }

  /**
   * Update position values based on current prices
   */
  private async updatePositions(positions: Position[], priceData: TokenPrice[]): Promise<void> {
    const priceMap = new Map(priceData.map(p => [p.address, p]));

    for (const position of positions) {
      const price = priceMap.get(position.tokenAddress);
      if (!price) continue;

      try {
        // Calculate new values
        const currentValue = position.amount * price.price;
        const pnl = currentValue - position.entryValue;
        const pnlPercentage = calculatePercentageChange(position.entryValue, currentValue);
        const multiplier = calculateMultiplier(pnlPercentage);

        // Update position if values have changed significantly
        const priceChanged = Math.abs(price.price - position.currentPrice) / position.currentPrice > 0.001; // 0.1% change
        
        if (priceChanged) {
          await PortfolioService.updatePosition(position.id, {
            currentPrice: price.price,
            currentValue,
            pnl,
            pnlPercentage,
            multiplier,
          });
        }
      } catch (error) {
        console.error(`Error updating position ${position.id}:`, error);
      }
    }

    // Update portfolio totals
    await this.updatePortfolioTotals();
  }

  /**
   * Update portfolio totals
   */
  private async updatePortfolioTotals(): Promise<void> {
    try {
      const activePositions = await PortfolioService.getActivePositions();
      
      const totalValue = activePositions.reduce((sum, pos) => sum + pos.currentValue, 0);
      const totalPnL = activePositions.reduce((sum, pos) => sum + pos.pnl, 0);
      const totalEntryValue = activePositions.reduce((sum, pos) => sum + pos.entryValue, 0);
      const totalPnLPercentage = totalEntryValue > 0 ? (totalPnL / totalEntryValue) * 100 : 0;

      await PortfolioService.updatePortfolioTotals(totalValue, totalPnL, totalPnLPercentage);
    } catch (error) {
      console.error('Error updating portfolio totals:', error);
    }
  }

  /**
   * Check for significant price movements and alerts
   */
  private async checkPriceAlerts(priceData: TokenPrice[], signals: TelegramSignal[]): Promise<void> {
    const signalMap = new Map(
      signals
        .filter(s => s.tokenAddress)
        .map(s => [s.tokenAddress!, s])
    );

    for (const price of priceData) {
      const signal = signalMap.get(price.address);
      if (!signal) continue;

      try {
        // Calculate multiplier from signal time
        const signalPrice = this.getSignalPrice(price.address, signal.timestamp);
        if (signalPrice) {
          const multiplier = price.price / signalPrice;
          
          // Check for significant milestones and trigger notifications
          if (multiplier >= 2 && multiplier < 2.1) {
            console.log(`🚀 ${signal.tokenSymbol || price.address} reached 2x!`);
            // Notification service will handle milestone alerts
          } else if (multiplier >= 5 && multiplier < 5.1) {
            console.log(`🚀🚀 ${signal.tokenSymbol || price.address} reached 5x!`);
            // Notification service will handle milestone alerts
          } else if (multiplier >= 10 && multiplier < 10.1) {
            console.log(`🚀🚀🚀 ${signal.tokenSymbol || price.address} reached 10x!`);
            // Notification service will handle milestone alerts
          }
        }

        // Check for significant price changes
        if (Math.abs(price.priceChange24h) > 50) {
          console.log(`📈 ${signal.tokenSymbol || price.address} moved ${price.priceChange24h.toFixed(2)}% in 24h`);
          // TODO: Send notification
        }
      } catch (error) {
        console.error(`Error checking price alerts for ${price.address}:`, error);
      }
    }
  }

  /**
   * Get price at signal time (estimated)
   */
  private getSignalPrice(tokenAddress: string, signalTime: Date): number | null {
    const history = this.priceHistory.get(tokenAddress);
    if (!history || history.length === 0) return null;

    // Find the closest price data to signal time
    const signalTimestamp = signalTime.getTime();
    let closest = history[0];
    let minDiff = Math.abs(closest.timestamp.getTime() - signalTimestamp);

    for (const price of history) {
      const diff = Math.abs(price.timestamp.getTime() - signalTimestamp);
      if (diff < minDiff) {
        minDiff = diff;
        closest = price;
      }
    }

    return closest.price;
  }

  /**
   * Get price history for a token
   */
  getPriceHistory(tokenAddress: string): TokenPrice[] {
    return this.priceHistory.get(tokenAddress) || [];
  }

  /**
   * Get current price for a token
   */
  getCurrentPrice(tokenAddress: string): number | null {
    const history = this.priceHistory.get(tokenAddress);
    if (!history || history.length === 0) return null;
    
    return history[history.length - 1].price;
  }

  /**
   * Set update interval
   */
  setUpdateInterval(intervalMs: number): void {
    this.updateInterval = intervalMs;
    
    if (this.isRunning) {
      this.stop();
      this.start();
    }
  }

  /**
   * Get monitoring status
   */
  getStatus(): {
    isRunning: boolean;
    trackedTokensCount: number;
    updateInterval: number;
    priceHistorySize: number;
  } {
    return {
      isRunning: this.isRunning,
      trackedTokensCount: this.trackedTokens.size,
      updateInterval: this.updateInterval,
      priceHistorySize: this.priceHistory.size,
    };
  }
}
