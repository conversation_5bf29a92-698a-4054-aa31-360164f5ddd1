/**
 * Supabase Connection Test for Signal V1
 * Tests the real Supabase project connection
 */

(function() {
  console.log('🔗 Testing Supabase Connection...');
  
  const testResults = {
    connection: { tested: false, passed: false, details: {} },
    authentication: { tested: false, passed: false, details: {} },
    database: { tested: false, passed: false, details: {} }
  };

  // Test 1: Basic Connection
  async function testSupabaseConnection() {
    console.log('\n🔗 Testing Supabase Connection...');
    
    try {
      // Check if Supabase client is available
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ccbfqeollvwzrwkzkilz.supabase.co';
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjYmZxZW9sbHZ3enJ3a3praWx6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNzg5OTksImV4cCI6MjA2Njc1NDk5OX0.h8k5g7YMbY3QsyUPzTtH-q9q9pnmjBQcF8ri8WnYPUA';
      
      console.log(`📡 Supabase URL: ${supabaseUrl}`);
      console.log(`🔑 Anon Key: ${supabaseKey.slice(0, 20)}...`);
      
      // Test basic connectivity
      const response = await fetch(`${supabaseUrl}/rest/v1/`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        console.log('✅ Supabase connection successful');
        console.log(`📊 Status: ${response.status} ${response.statusText}`);
        
        testResults.connection.passed = true;
        testResults.connection.details = {
          url: supabaseUrl,
          status: response.status,
          statusText: response.statusText
        };
      } else {
        throw new Error(`Connection failed: ${response.status} ${response.statusText}`);
      }
      
      testResults.connection.tested = true;
      
    } catch (error) {
      console.error('❌ Supabase connection failed:', error);
      testResults.connection.tested = true;
      testResults.connection.passed = false;
      testResults.connection.details = { error: error.message };
    }
  }

  // Test 2: Database Access
  async function testDatabaseAccess() {
    console.log('\n🗄️ Testing Database Access...');
    
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ccbfqeollvwzrwkzkilz.supabase.co';
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjYmZxZW9sbHZ3enJ3a3praWx6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNzg5OTksImV4cCI6MjA2Njc1NDk5OX0.h8k5g7YMbY3QsyUPzTtH-q9q9pnmjBQcF8ri8WnYPUA';
      
      // Try to access a common table (this might fail if tables don't exist yet)
      const response = await fetch(`${supabaseUrl}/rest/v1/signals?limit=1`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ Database access successful');
        console.log(`📊 Response: ${JSON.stringify(data).slice(0, 100)}...`);
        
        testResults.database.passed = true;
        testResults.database.details = {
          tableAccess: true,
          dataLength: Array.isArray(data) ? data.length : 'unknown'
        };
      } else if (response.status === 404) {
        console.log('ℹ️ Database accessible but signals table not found (expected for new project)');
        testResults.database.passed = true;
        testResults.database.details = {
          tableAccess: false,
          reason: 'Table not found (expected for new project)'
        };
      } else {
        throw new Error(`Database access failed: ${response.status} ${response.statusText}`);
      }
      
      testResults.database.tested = true;
      
    } catch (error) {
      console.error('❌ Database access failed:', error);
      testResults.database.tested = true;
      testResults.database.passed = false;
      testResults.database.details = { error: error.message };
    }
  }

  // Test 3: Authentication
  async function testAuthentication() {
    console.log('\n🔐 Testing Authentication...');
    
    try {
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://ccbfqeollvwzrwkzkilz.supabase.co';
      const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNjYmZxZW9sbHZ3enJ3a3praWx6Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExNzg5OTksImV4cCI6MjA2Njc1NDk5OX0.h8k5g7YMbY3QsyUPzTtH-q9q9pnmjBQcF8ri8WnYPUA';
      
      // Test auth endpoint
      const response = await fetch(`${supabaseUrl}/auth/v1/user`, {
        method: 'GET',
        headers: {
          'apikey': supabaseKey,
          'Authorization': `Bearer ${supabaseKey}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.status === 401) {
        console.log('✅ Authentication endpoint accessible (401 expected for anonymous user)');
        testResults.authentication.passed = true;
        testResults.authentication.details = {
          endpointAccessible: true,
          status: response.status,
          reason: 'Anonymous user (expected)'
        };
      } else if (response.ok) {
        const data = await response.json();
        console.log('✅ Authentication successful');
        console.log(`👤 User data: ${JSON.stringify(data).slice(0, 100)}...`);
        
        testResults.authentication.passed = true;
        testResults.authentication.details = {
          authenticated: true,
          userData: data
        };
      } else {
        throw new Error(`Authentication test failed: ${response.status} ${response.statusText}`);
      }
      
      testResults.authentication.tested = true;
      
    } catch (error) {
      console.error('❌ Authentication test failed:', error);
      testResults.authentication.tested = true;
      testResults.authentication.passed = false;
      testResults.authentication.details = { error: error.message };
    }
  }

  // Test 4: Environment Variables
  function testEnvironmentVariables() {
    console.log('\n🌍 Testing Environment Variables...');
    
    const expectedUrl = 'https://ccbfqeollvwzrwkzkilz.supabase.co';
    const expectedKeyPrefix = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9';
    
    const actualUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const actualKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    
    console.log(`🔗 Expected URL: ${expectedUrl}`);
    console.log(`🔗 Actual URL: ${actualUrl || 'NOT SET'}`);
    console.log(`🔑 Key starts correctly: ${actualKey?.startsWith(expectedKeyPrefix) ? 'YES' : 'NO'}`);
    
    if (actualUrl === expectedUrl && actualKey?.startsWith(expectedKeyPrefix)) {
      console.log('✅ Environment variables configured correctly');
      return true;
    } else {
      console.log('⚠️ Environment variables may need updating');
      return false;
    }
  }

  // Run all tests
  async function runAllTests() {
    console.log('🧪 Starting Supabase Connection Tests...');
    
    // Test environment variables first
    const envOk = testEnvironmentVariables();
    
    if (!envOk) {
      console.log('\n⚠️ Environment variables not properly configured. Tests may fail.');
    }
    
    // Run async tests
    await testSupabaseConnection();
    
    setTimeout(async () => {
      await testDatabaseAccess();
      
      setTimeout(async () => {
        await testAuthentication();
        
        // Generate final report
        setTimeout(() => {
          console.log('\n📊 SUPABASE CONNECTION TEST REPORT');
          console.log('===================================');
          
          Object.entries(testResults).forEach(([testName, result]) => {
            const status = result.tested ? (result.passed ? '✅ PASS' : '❌ FAIL') : '⏭️ SKIP';
            const name = testName.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            
            console.log(`${status} - ${name}`);
            
            if (result.details && Object.keys(result.details).length > 0) {
              Object.entries(result.details).forEach(([key, value]) => {
                console.log(`  • ${key}: ${JSON.stringify(value)}`);
              });
            }
          });
          
          const testedCount = Object.values(testResults).filter(r => r.tested).length;
          const passedCount = Object.values(testResults).filter(r => r.tested && r.passed).length;
          
          console.log(`\n🎯 Overall: ${passedCount}/${testedCount} tests passed`);
          
          if (passedCount === testedCount) {
            console.log('🎉 Supabase connection is working correctly!');
          } else {
            console.log('⚠️ Some Supabase connection issues detected');
          }
          
          // Store results for later access
          window.supabaseTestResults = testResults;
          
          console.log('\n📋 Access full results with: window.supabaseTestResults');
          console.log('✨ Supabase connection testing completed!');
          
        }, 1000);
      }, 2000);
    }, 1000);
  }
  
  // Start tests
  runAllTests();
  
})();
