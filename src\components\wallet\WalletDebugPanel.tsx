'use client';

import React, { useState, useEffect } from 'react';
import { useWallet } from '@/hooks/useWallet';
import { checkWalletReadiness, getAvailableWallets } from '@/lib/wallet/config';
import { Button } from '@/components/ui/Button';

export function WalletDebugPanel() {
  const {
    connected,
    connecting,
    disconnecting,
    publicKey,
    balance,
    loading,
    error,
    connect,
    disconnect,
    recoverWalletState,
    fetchBalances
  } = useWallet();
  const [walletReadiness, setWalletReadiness] = useState<any>({});
  const [availableWallets, setAvailableWallets] = useState<any[]>([]);
  const [stateHistory, setStateHistory] = useState<any[]>([]);

  useEffect(() => {
    if (typeof window !== 'undefined') {
      setWalletReadiness(checkWalletReadiness());
      setAvailableWallets(getAvailableWallets());
    }
  }, []);

  // Track wallet state changes
  useEffect(() => {
    const newState = {
      timestamp: new Date().toLocaleTimeString(),
      connected,
      connecting,
      disconnecting,
      publicKey: publicKey?.toString().slice(0, 8) + '...',
      balance: balance?.sol || 0,
      loading,
      error: error?.slice(0, 50) || null
    };

    setStateHistory(prev => [newState, ...prev.slice(0, 9)]); // Keep last 10 states
  }, [connected, connecting, disconnecting, publicKey, balance, loading, error]);

  const refreshWalletStatus = () => {
    if (typeof window !== 'undefined') {
      setWalletReadiness(checkWalletReadiness());
      setAvailableWallets(getAvailableWallets());
    }
  };

  return (
    <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold text-white">Wallet Debug Panel</h3>
        <Button onClick={refreshWalletStatus} size="sm" variant="outline">
          Refresh
        </Button>
      </div>

      {/* Connection Status */}
      <div className="space-y-2">
        <h4 className="font-medium text-green-400">Connection Status</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="text-gray-300">Connected:</div>
          <div className={connected ? 'text-green-400' : 'text-red-400'}>
            {connected ? 'Yes' : 'No'}
          </div>
          <div className="text-gray-300">Connecting:</div>
          <div className={connecting ? 'text-yellow-400' : 'text-gray-400'}>
            {connecting ? 'Yes' : 'No'}
          </div>
          <div className="text-gray-300">Public Key:</div>
          <div className="text-gray-400 font-mono text-xs">
            {publicKey ? `${publicKey.toString().slice(0, 8)}...` : 'None'}
          </div>
        </div>
      </div>

      {/* Error Status */}
      {error && (
        <div className="space-y-2">
          <h4 className="font-medium text-red-400">Error</h4>
          <div className="text-sm text-red-300 bg-red-900/20 p-2 rounded">
            {error}
          </div>
        </div>
      )}

      {/* Wallet Readiness */}
      <div className="space-y-2">
        <h4 className="font-medium text-blue-400">Wallet Readiness</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          {Object.entries(walletReadiness).map(([wallet, ready]) => (
            <React.Fragment key={wallet}>
              <div className="text-gray-300 capitalize">{wallet}:</div>
              <div className={ready ? 'text-green-400' : 'text-red-400'}>
                {ready ? 'Ready' : 'Not Ready'}
              </div>
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Available Wallets */}
      <div className="space-y-2">
        <h4 className="font-medium text-purple-400">Available Wallets</h4>
        {availableWallets.length > 0 ? (
          <div className="space-y-1">
            {availableWallets.map((wallet) => (
              <div key={wallet.name} className="text-sm text-gray-300">
                • {wallet.name}
              </div>
            ))}
          </div>
        ) : (
          <div className="text-sm text-gray-500">No wallets detected</div>
        )}
      </div>

      {/* Enhanced State Information */}
      <div className="space-y-2">
        <h4 className="font-medium text-cyan-400">Enhanced State</h4>
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div className="text-gray-300">Loading:</div>
          <div className={loading ? 'text-yellow-400' : 'text-gray-500'}>
            {loading ? 'Yes' : 'No'}
          </div>
          <div className="text-gray-300">Disconnecting:</div>
          <div className={disconnecting ? 'text-yellow-400' : 'text-gray-500'}>
            {disconnecting ? 'Yes' : 'No'}
          </div>
          <div className="text-gray-300">Balance:</div>
          <div className="text-white">
            {balance ? `${balance.sol.toFixed(4)} SOL` : 'None'}
          </div>
        </div>
      </div>

      {/* State History */}
      <div className="space-y-2">
        <h4 className="font-medium text-purple-400">State History</h4>
        <div className="max-h-32 overflow-y-auto space-y-1">
          {stateHistory.map((state, index) => (
            <div key={index} className="text-xs text-gray-400 border-l-2 border-gray-600 pl-2">
              <div className="text-gray-300">{state.timestamp}</div>
              <div>Connected: {state.connected ? '✅' : '❌'} | Loading: {state.loading ? '⏳' : '✅'}</div>
              {state.error && <div className="text-red-400">Error: {state.error}</div>}
            </div>
          ))}
        </div>
      </div>

      {/* Actions */}
      <div className="space-y-2">
        <h4 className="font-medium text-yellow-400">Actions</h4>
        <div className="flex flex-wrap gap-2">
          {!connected ? (
            <Button onClick={connect} disabled={connecting} size="sm">
              {connecting ? 'Connecting...' : 'Connect'}
            </Button>
          ) : (
            <Button onClick={disconnect} size="sm" variant="outline">
              Disconnect
            </Button>
          )}
          <Button onClick={fetchBalances} disabled={!connected || loading} size="sm" variant="outline">
            Refresh Balance
          </Button>
          <Button onClick={recoverWalletState} size="sm" variant="outline">
            Recover State
          </Button>
        </div>
      </div>
    </div>
  );
}
