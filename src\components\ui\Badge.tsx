'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: 'default' | 'secondary' | 'success' | 'warning' | 'error' | 'outline' | 'moon' | 'rekt' | 'alpha' | 'diamond';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
}

export function Badge({
  className,
  variant = 'default',
  size = 'md',
  children,
  ...props
}: BadgeProps) {
  const baseClasses = 'inline-flex items-center font-medium rounded-full transition-all duration-300 shadow-sm';

  const variants = {
    default: 'bg-gradient-neutral text-white shadow-lg',
    secondary: 'bg-gradient-degen text-secondary border border-subtle',
    success: 'bg-gradient-profit text-white shadow-lg glow-green',
    warning: 'bg-gradient-premium text-neutral-900 shadow-lg glow-gold',
    error: 'bg-gradient-loss text-white shadow-lg glow-red',
    outline: 'border border-accent-primary/50 text-accent-primary bg-transparent hover:bg-accent-primary/10',
    moon: 'bg-gradient-profit text-white shadow-lg glow-green',
    rekt: 'bg-gradient-loss text-white shadow-lg glow-red',
    alpha: 'bg-gradient-signal text-white shadow-lg glow-purple',
    diamond: 'bg-gradient-premium text-neutral-900 shadow-lg glow-gold font-bold',
  };

  const sizes = {
    sm: 'px-2 py-0.5 text-xs',
    md: 'px-2.5 py-1 text-sm',
    lg: 'px-3 py-1.5 text-base',
  };

  return (
    <div
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
}
