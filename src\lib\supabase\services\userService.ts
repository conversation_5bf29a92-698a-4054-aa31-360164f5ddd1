// User Service - Handles user-related database operations

import { supabase } from '../client';
import type { User, UserPreferences } from '@/types';

export class UserService {
  /**
   * Get current user profile
   */
  static async getCurrentUserProfile(): Promise<User | null> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return null;
      }

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user profile:', error);
        return null;
      }

      return {
        id: data.id,
        email: data.email,
        walletAddress: data.wallet_address,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        preferences: data.preferences as UserPreferences,
      };
    } catch (error) {
      console.error('Error in getCurrentUserProfile:', error);
      return null;
    }
  }

  /**
   * Update user profile
   */
  static async updateUserProfile(updates: Partial<User>): Promise<boolean> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return false;
      }

      const updateData: any = {};
      
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.walletAddress !== undefined) updateData.wallet_address = updates.walletAddress;
      if (updates.preferences !== undefined) updateData.preferences = updates.preferences;

      const { error } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', user.id);

      if (error) {
        console.error('Error updating user profile:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error in updateUserProfile:', error);
      return false;
    }
  }

  /**
   * Update user preferences
   */
  static async updateUserPreferences(preferences: Partial<UserPreferences>): Promise<boolean> {
    try {
      const currentProfile = await this.getCurrentUserProfile();
      if (!currentProfile) return false;

      const updatedPreferences = {
        ...currentProfile.preferences,
        ...preferences,
      };

      return await this.updateUserProfile({ preferences: updatedPreferences });
    } catch (error) {
      console.error('Error in updateUserPreferences:', error);
      return false;
    }
  }

  /**
   * Link wallet address to user
   */
  static async linkWalletAddress(walletAddress: string): Promise<boolean> {
    try {
      return await this.updateUserProfile({ walletAddress });
    } catch (error) {
      console.error('Error in linkWalletAddress:', error);
      return false;
    }
  }

  /**
   * Get user by wallet address
   */
  static async getUserByWalletAddress(walletAddress: string): Promise<User | null> {
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('wallet_address', walletAddress)
        .single();

      if (error) {
        console.error('Error fetching user by wallet:', error);
        return null;
      }

      return {
        id: data.id,
        email: data.email,
        walletAddress: data.wallet_address,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        preferences: data.preferences as UserPreferences,
      };
    } catch (error) {
      console.error('Error in getUserByWalletAddress:', error);
      return null;
    }
  }

  /**
   * Delete user account
   */
  static async deleteUserAccount(): Promise<boolean> {
    try {
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.error('Auth error:', authError);
        return false;
      }

      // Delete user profile (cascades to portfolios, positions, trades)
      const { error: deleteError } = await supabase
        .from('users')
        .delete()
        .eq('id', user.id);

      if (deleteError) {
        console.error('Error deleting user profile:', deleteError);
        return false;
      }

      // Sign out user
      await supabase.auth.signOut();
      
      return true;
    } catch (error) {
      console.error('Error in deleteUserAccount:', error);
      return false;
    }
  }

  /**
   * Subscribe to user profile changes
   */
  static subscribeToUserProfile(
    userId: string,
    callback: (user: User | null) => void
  ) {
    return supabase
      .channel(`user-profile-${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'users',
          filter: `id=eq.${userId}`,
        },
        async (payload) => {
          if (payload.eventType === 'DELETE') {
            callback(null);
          } else {
            const data = payload.new as any;
            callback({
              id: data.id,
              email: data.email,
              walletAddress: data.wallet_address,
              createdAt: new Date(data.created_at),
              updatedAt: new Date(data.updated_at),
              preferences: data.preferences as UserPreferences,
            });
          }
        }
      )
      .subscribe();
  }
}
