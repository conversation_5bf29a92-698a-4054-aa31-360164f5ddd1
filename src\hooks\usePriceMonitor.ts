'use client';

import { useEffect, useState, useCallback } from 'react';
import { PriceMonitorService } from '@/lib/services/priceMonitor';
import { PriceTracker } from '@/lib/jupiter/api';
import type { TokenPrice } from '@/types';

export function usePriceMonitor() {
  const [isRunning, setIsRunning] = useState(false);
  const [status, setStatus] = useState(PriceMonitorService.getInstance().getStatus());

  // Update status periodically
  useEffect(() => {
    const updateStatus = () => {
      setStatus(PriceMonitorService.getInstance().getStatus());
      setIsRunning(PriceMonitorService.getInstance().getStatus().isRunning);
    };

    updateStatus();
    const interval = setInterval(updateStatus, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, []);

  const start = useCallback(async () => {
    try {
      await PriceMonitorService.getInstance().start();
      setIsRunning(true);
    } catch (error) {
      console.error('Failed to start price monitor:', error);
    }
  }, []);

  const stop = useCallback(() => {
    PriceMonitorService.getInstance().stop();
    setIsRunning(false);
  }, []);

  const addToken = useCallback((tokenAddress: string) => {
    PriceMonitorService.getInstance().addToken(tokenAddress);
  }, []);

  const removeToken = useCallback((tokenAddress: string) => {
    PriceMonitorService.getInstance().removeToken(tokenAddress);
  }, []);

  const getTrackedTokens = useCallback(() => {
    return PriceMonitorService.getInstance().getTrackedTokens();
  }, []);

  const getPriceHistory = useCallback((tokenAddress: string) => {
    return PriceMonitorService.getInstance().getPriceHistory(tokenAddress);
  }, []);

  const getCurrentPrice = useCallback((tokenAddress: string) => {
    return PriceMonitorService.getInstance().getCurrentPrice(tokenAddress);
  }, []);

  return {
    isRunning,
    status,
    start,
    stop,
    addToken,
    removeToken,
    getTrackedTokens,
    getPriceHistory,
    getCurrentPrice,
  };
}

export function useTokenPrice(tokenAddress: string) {
  const [price, setPrice] = useState<TokenPrice | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPrice = useCallback(async () => {
    if (!tokenAddress) return;

    try {
      setLoading(true);
      setError(null);
      
      const priceData = await PriceTracker.getTokenPriceData(tokenAddress);
      setPrice(priceData);
    } catch (err) {
      console.error('Error fetching token price:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch price');
    } finally {
      setLoading(false);
    }
  }, [tokenAddress]);

  useEffect(() => {
    fetchPrice();
  }, [fetchPrice]);

  return {
    price,
    loading,
    error,
    refetch: fetchPrice,
  };
}

export function useMultipleTokenPrices(tokenAddresses: string[]) {
  const [prices, setPrices] = useState<TokenPrice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPrices = useCallback(async () => {
    if (!tokenAddresses.length) {
      setPrices([]);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      const priceData = await PriceTracker.getMultipleTokenPriceData(tokenAddresses);
      setPrices(priceData);
    } catch (err) {
      console.error('Error fetching token prices:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch prices');
    } finally {
      setLoading(false);
    }
  }, [tokenAddresses]);

  useEffect(() => {
    fetchPrices();
  }, [fetchPrices]);

  return {
    prices,
    loading,
    error,
    refetch: fetchPrices,
  };
}

export function useLivePrice(tokenAddress: string, updateInterval: number = 30000) {
  const [price, setPrice] = useState<number | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);

  useEffect(() => {
    if (!tokenAddress) return;

    const updatePrice = async () => {
      try {
        const currentPrice = PriceMonitorService.getInstance().getCurrentPrice(tokenAddress);
        if (currentPrice !== null) {
          setPrice(currentPrice);
          setLastUpdate(new Date());
        } else {
          // Fallback to direct API call
          const priceData = await PriceTracker.getTokenPriceData(tokenAddress);
          if (priceData) {
            setPrice(priceData.price);
            setLastUpdate(new Date());
          }
        }
      } catch (error) {
        console.error('Error updating live price:', error);
      }
    };

    // Initial update
    updatePrice();

    // Set up interval
    const interval = setInterval(updatePrice, updateInterval);

    return () => clearInterval(interval);
  }, [tokenAddress, updateInterval]);

  return {
    price,
    lastUpdate,
  };
}
