<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Buttons & Supabase Test - Signal V1</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 16px;
            padding: 32px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 32px;
            color: #10b981;
        }
        
        .test-section {
            margin-bottom: 24px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 12px;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #60a5fa;
        }
        
        button {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
            margin-right: 12px;
            margin-bottom: 12px;
        }
        
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
        }
        
        button.secondary {
            background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
        }
        
        button.danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        }
        
        .result {
            margin-top: 16px;
            padding: 12px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        
        .result.success {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            color: #10b981;
        }
        
        .result.error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            color: #ef4444;
        }
        
        .result.info {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            color: #3b82f6;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 16px;
            margin-top: 16px;
        }
        
        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 16px;
            text-align: center;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success { background: #10b981; }
        .status-indicator.error { background: #ef4444; }
        .status-indicator.pending { background: #f59e0b; animation: pulse 2s infinite; }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        
        .link-button {
            display: inline-block;
            text-decoration: none;
            color: white;
            background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            margin: 4px;
            transition: all 0.2s;
        }
        
        .link-button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Trading Buttons & Supabase Integration Test</h1>
        
        <div class="test-section">
            <h3>📊 Application Status</h3>
            <div class="status-grid">
                <div class="status-card">
                    <div><span class="status-indicator pending"></span>Signals Page</div>
                    <div id="signalsStatus">Checking...</div>
                </div>
                <div class="status-card">
                    <div><span class="status-indicator pending"></span>Trading Buttons</div>
                    <div id="tradingStatus">Checking...</div>
                </div>
                <div class="status-card">
                    <div><span class="status-indicator pending"></span>Supabase</div>
                    <div id="supabaseStatus">Checking...</div>
                </div>
                <div class="status-card">
                    <div><span class="status-indicator pending"></span>Navigation</div>
                    <div id="navigationStatus">Checking...</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔗 Quick Navigation</h3>
            <a href="/signals" class="link-button" target="_blank">Live Alpha Signals</a>
            <a href="/trading" class="link-button" target="_blank">Trading Page</a>
            <a href="/settings" class="link-button" target="_blank">Settings</a>
            <a href="/" class="link-button" target="_blank">Home</a>
        </div>
        
        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <button onclick="testTradingButtons()">Test Trading Buttons</button>
            <button onclick="testSupabaseConnection()">Test Supabase Connection</button>
            <button onclick="testNavigation()">Test Navigation</button>
            <button onclick="runAllTests()" class="secondary">Run All Tests</button>
            <div id="testResults" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>🔍 Manual Verification</h3>
            <p>Follow these steps to manually verify the fixes:</p>
            <ol style="line-height: 1.6;">
                <li><strong>Visit Signals Page:</strong> <a href="/signals" target="_blank" class="link-button">Open /signals</a></li>
                <li><strong>Check Token Cards:</strong> Verify each signal card has BUY and SELL buttons</li>
                <li><strong>Test Trading Flow:</strong> Click a BUY button and verify it navigates to trading page</li>
                <li><strong>Verify Supabase:</strong> Check console for any Supabase connection errors</li>
                <li><strong>Test Wallet Integration:</strong> Connect wallet and test trading functionality</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>📋 Expected Results</h3>
            <div class="result info">
✅ Signal cards should display BUY 🚀 and SELL 💰 buttons
✅ Clicking trading buttons should navigate to /trading page with pre-filled data
✅ Supabase should connect to: https://ccbfqeollvwzrwkzkilz.supabase.co
✅ No console errors related to missing trading buttons
✅ TokenCard components should replace custom signal cards
✅ All existing functionality should be preserved
            </div>
        </div>
    </div>

    <script>
        let testResults = {};
        
        // Test Trading Buttons
        async function testTradingButtons() {
            showResult('Testing trading buttons functionality...', 'info');
            
            try {
                // Check if we can access the signals page
                const response = await fetch('/signals');
                if (!response.ok) {
                    throw new Error(`Signals page not accessible: ${response.status}`);
                }
                
                // Open signals page in new tab for manual verification
                const signalsWindow = window.open('/signals', '_blank');
                
                // Wait a bit then check if window opened
                setTimeout(() => {
                    if (signalsWindow) {
                        showResult(
                            '✅ Trading buttons test initiated!\n\n' +
                            'Manual verification required:\n' +
                            '1. Check the opened signals page\n' +
                            '2. Verify each token card has BUY and SELL buttons\n' +
                            '3. Click a BUY button to test navigation\n' +
                            '4. Verify it navigates to /trading with token data\n\n' +
                            'Expected: BUY 🚀 and SELL 💰 buttons on each token card',
                            'success'
                        );
                        updateStatus('tradingStatus', 'Manual check required', 'pending');
                    } else {
                        throw new Error('Could not open signals page for verification');
                    }
                }, 1000);
                
                testResults.tradingButtons = { passed: true, manual: true };
                
            } catch (error) {
                showResult(`❌ Trading buttons test failed: ${error.message}`, 'error');
                testResults.tradingButtons = { passed: false, error: error.message };
                updateStatus('tradingStatus', 'Failed', 'error');
            }
        }
        
        // Test Supabase Connection
        async function testSupabaseConnection() {
            showResult('Testing Supabase connection...', 'info');
            
            try {
                // Load and run the Supabase test script
                const response = await fetch('/test-supabase-connection.js');
                if (!response.ok) {
                    throw new Error('Could not load Supabase test script');
                }
                
                const script = await response.text();
                eval(script);
                
                showResult(
                    '✅ Supabase connection test started!\n\n' +
                    'Check browser console for detailed results.\n' +
                    'The test will verify:\n' +
                    '• Basic connection to Supabase\n' +
                    '• Database access\n' +
                    '• Authentication endpoints\n' +
                    '• Environment variables\n\n' +
                    'Results will be available in window.supabaseTestResults',
                    'success'
                );
                
                testResults.supabase = { passed: true, automated: true };
                updateStatus('supabaseStatus', 'Testing...', 'pending');
                
                // Check results after a delay
                setTimeout(() => {
                    if (window.supabaseTestResults) {
                        const results = window.supabaseTestResults;
                        const passed = Object.values(results).every(r => r.passed);
                        updateStatus('supabaseStatus', passed ? 'Connected' : 'Issues detected', passed ? 'success' : 'error');
                    }
                }, 5000);
                
            } catch (error) {
                showResult(`❌ Supabase test failed: ${error.message}`, 'error');
                testResults.supabase = { passed: false, error: error.message };
                updateStatus('supabaseStatus', 'Failed', 'error');
            }
        }
        
        // Test Navigation
        async function testNavigation() {
            showResult('Testing navigation functionality...', 'info');
            
            try {
                const pages = ['/signals', '/trading', '/settings', '/'];
                const results = [];
                
                for (const page of pages) {
                    try {
                        const response = await fetch(page);
                        results.push({
                            page,
                            status: response.status,
                            ok: response.ok
                        });
                    } catch (error) {
                        results.push({
                            page,
                            status: 'error',
                            ok: false,
                            error: error.message
                        });
                    }
                }
                
                const allOk = results.every(r => r.ok);
                
                showResult(
                    '📊 Navigation Test Results:\n\n' +
                    results.map(r => 
                        `${r.ok ? '✅' : '❌'} ${r.page}: ${r.status} ${r.error ? `(${r.error})` : ''}`
                    ).join('\n') +
                    `\n\n${allOk ? '🎉 All pages accessible!' : '⚠️ Some pages have issues'}`,
                    allOk ? 'success' : 'error'
                );
                
                testResults.navigation = { passed: allOk, results };
                updateStatus('navigationStatus', allOk ? 'All pages OK' : 'Issues found', allOk ? 'success' : 'error');
                
            } catch (error) {
                showResult(`❌ Navigation test failed: ${error.message}`, 'error');
                testResults.navigation = { passed: false, error: error.message };
                updateStatus('navigationStatus', 'Failed', 'error');
            }
        }
        
        // Run All Tests
        async function runAllTests() {
            showResult('Running comprehensive test suite...', 'info');
            
            updateStatus('signalsStatus', 'Testing...', 'pending');
            updateStatus('tradingStatus', 'Testing...', 'pending');
            updateStatus('supabaseStatus', 'Testing...', 'pending');
            updateStatus('navigationStatus', 'Testing...', 'pending');
            
            await testNavigation();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testSupabaseConnection();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            await testTradingButtons();
            
            // Final summary
            setTimeout(() => {
                const totalTests = Object.keys(testResults).length;
                const passedTests = Object.values(testResults).filter(r => r.passed).length;
                
                showResult(
                    `🎯 Test Suite Complete!\n\n` +
                    `📊 Results: ${passedTests}/${totalTests} tests passed\n\n` +
                    `${passedTests === totalTests ? '🎉 All tests passed!' : '⚠️ Some tests need attention'}\n\n` +
                    `Check individual test results above for details.`,
                    passedTests === totalTests ? 'success' : 'error'
                );
                
                updateStatus('signalsStatus', 'Tested', 'success');
                
            }, 3000);
        }
        
        // Helper Functions
        function showResult(message, type) {
            const div = document.getElementById('testResults');
            div.textContent = message;
            div.className = `result ${type}`;
            div.style.display = 'block';
        }
        
        function updateStatus(elementId, text, status) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = text;
                const indicator = element.parentElement.querySelector('.status-indicator');
                if (indicator) {
                    indicator.className = `status-indicator ${status}`;
                }
            }
        }
        
        // Auto-run basic checks on load
        window.addEventListener('load', () => {
            setTimeout(() => {
                testNavigation();
            }, 1000);
        });
    </script>
</body>
</html>
