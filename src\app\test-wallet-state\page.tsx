'use client';

import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { WalletButton } from '@/components/wallet/WalletButton';
import { useWallet } from '@/hooks/useWallet';
import { walletPersistence } from '@/lib/wallet/persistence';
import Link from 'next/link';

export default function TestWalletStatePage() {
  const { 
    connected, 
    connecting, 
    isRestoring, 
    publicKey, 
    balance, 
    error, 
    connect, 
    disconnect,
    restoreWalletState 
  } = useWallet();
  
  const [persistenceInfo, setPersistenceInfo] = useState<any>(null);
  const [testResults, setTestResults] = useState<string[]>([]);

  // Update persistence info periodically
  useEffect(() => {
    const updateInfo = () => {
      const state = walletPersistence.loadWalletState();
      const session = walletPersistence.loadWalletSession();
      setPersistenceInfo({ state, session });
    };

    updateInfo();
    const interval = setInterval(updateInfo, 1000);
    return () => clearInterval(interval);
  }, []);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const testWalletPersistence = async () => {
    addTestResult('🧪 Starting wallet persistence test...');
    
    if (!connected) {
      addTestResult('❌ Please connect wallet first');
      return;
    }

    // Test 1: Check if state is saved
    const state = walletPersistence.loadWalletState();
    if (state?.isConnected) {
      addTestResult('✅ Wallet state is properly saved');
    } else {
      addTestResult('❌ Wallet state not saved correctly');
    }

    // Test 2: Check session info
    const session = walletPersistence.loadWalletSession();
    if (session) {
      addTestResult('✅ Wallet session is active');
    } else {
      addTestResult('❌ No active wallet session');
    }

    // Test 3: Check auto-reconnect flag
    if (walletPersistence.shouldAutoReconnect()) {
      addTestResult('✅ Auto-reconnect is enabled');
    } else {
      addTestResult('❌ Auto-reconnect is disabled');
    }

    addTestResult('🏁 Persistence test completed');
  };

  const testStateRestoration = async () => {
    addTestResult('🔄 Testing state restoration...');
    
    try {
      await restoreWalletState();
      addTestResult('✅ State restoration completed');
    } catch (error) {
      addTestResult(`❌ State restoration failed: ${error}`);
    }
  };

  const clearAllState = () => {
    walletPersistence.clearWalletState();
    addTestResult('🧹 All wallet state cleared');
  };

  const simulatePageNavigation = () => {
    addTestResult('🔄 Simulating page navigation...');
    // Simulate what happens during page navigation
    walletPersistence.updateActivity();
    addTestResult('✅ Activity updated for navigation');
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Wallet State Management Test
          </h1>
          <p className="text-gray-400">
            Test wallet connection persistence across page navigations and browser sessions
          </p>
        </div>

        {/* Wallet Status */}
        <Card>
          <CardHeader>
            <CardTitle>Current Wallet Status</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-gray-400">Connected:</span>
                <span className={`ml-2 font-bold ${connected ? 'text-green-400' : 'text-red-400'}`}>
                  {connected ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Connecting:</span>
                <span className={`ml-2 font-bold ${connecting ? 'text-yellow-400' : 'text-gray-400'}`}>
                  {connecting ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Restoring:</span>
                <span className={`ml-2 font-bold ${isRestoring ? 'text-blue-400' : 'text-gray-400'}`}>
                  {isRestoring ? 'Yes' : 'No'}
                </span>
              </div>
              <div>
                <span className="text-gray-400">Public Key:</span>
                <span className="ml-2 font-mono text-sm">
                  {publicKey ? `${publicKey.toString().slice(0, 8)}...` : 'None'}
                </span>
              </div>
            </div>

            {error && (
              <div className="p-3 bg-red-900/50 border border-red-500/50 rounded-lg">
                <p className="text-red-400 text-sm">{error}</p>
              </div>
            )}

            <div className="flex gap-2">
              <WalletButton />
              {connected && (
                <Button onClick={disconnect} variant="destructive">
                  Disconnect
                </Button>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Persistence Info */}
        <Card>
          <CardHeader>
            <CardTitle>Persistence Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <h4 className="font-semibold text-white mb-2">Stored State:</h4>
                <pre className="bg-gray-800 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(persistenceInfo?.state, null, 2)}
                </pre>
              </div>
              <div>
                <h4 className="font-semibold text-white mb-2">Active Session:</h4>
                <pre className="bg-gray-800 p-3 rounded text-sm overflow-auto">
                  {JSON.stringify(persistenceInfo?.session, null, 2)}
                </pre>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Test Controls */}
        <Card>
          <CardHeader>
            <CardTitle>Test Controls</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <Button onClick={testWalletPersistence} variant="outline">
                Test Persistence
              </Button>
              <Button onClick={testStateRestoration} variant="outline">
                Test Restoration
              </Button>
              <Button onClick={simulatePageNavigation} variant="outline">
                Simulate Navigation
              </Button>
              <Button onClick={clearAllState} variant="destructive">
                Clear All State
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Navigation Test Links */}
        <Card>
          <CardHeader>
            <CardTitle>Navigation Test</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-400 mb-4">
              Connect your wallet, then navigate to different pages to test state persistence:
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
              <Link href="/signals">
                <Button variant="outline" className="w-full">Signals</Button>
              </Link>
              <Link href="/trading">
                <Button variant="outline" className="w-full">Trading</Button>
              </Link>
              <Link href="/portfolio">
                <Button variant="outline" className="w-full">Portfolio</Button>
              </Link>
              <Link href="/settings">
                <Button variant="outline" className="w-full">Settings</Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        {/* Test Results */}
        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-800 p-4 rounded max-h-64 overflow-auto">
              {testResults.length === 0 ? (
                <p className="text-gray-400">No test results yet...</p>
              ) : (
                testResults.map((result, index) => (
                  <div key={index} className="text-sm font-mono mb-1">
                    {result}
                  </div>
                ))
              )}
            </div>
            {testResults.length > 0 && (
              <Button 
                onClick={() => setTestResults([])} 
                variant="outline" 
                size="sm" 
                className="mt-2"
              >
                Clear Results
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
