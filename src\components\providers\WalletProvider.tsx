'use client';

import React, { useMemo, useState, useEffect } from 'react';
import {
  <PERSON>Provider,
  WalletProvider as SolanaWalletProvider,
} from '@solana/wallet-adapter-react';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import { endpoint, getWallets, walletConfig } from '@/lib/wallet/config';

// Import wallet adapter CSS
import '@solana/wallet-adapter-react-ui/styles.css';

interface WalletProviderProps {
  children: React.ReactNode;
}

export function WalletProvider({ children }: WalletProviderProps) {
  const [wallets, setWallets] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // HMR-resilient wallet initialization with retry logic
  useEffect(() => {
    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 1000; // 1 second

    const initWallets = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Initializing wallet adapters...');
        const dynamicWallets = await getWallets();

        if (dynamicWallets.length === 0 && retryCount < maxRetries) {
          retryCount++;
          console.log(`⚠️ No wallets loaded, retrying (${retryCount}/${maxRetries})...`);
          setTimeout(initWallets, retryDelay);
          return;
        }

        setWallets(dynamicWallets);
        console.log(`✅ Wallet provider initialized with ${dynamicWallets.length} adapters`);
      } catch (error) {
        console.error('❌ Failed to initialize wallets:', error);
        setError(error instanceof Error ? error.message : 'Unknown wallet initialization error');

        // Enhanced retry logic for HMR-related errors
        if (retryCount < maxRetries && (
          error instanceof Error && (
            error.message.includes('module factory') ||
            error.message.includes('HMR') ||
            error.message.includes('chunk') ||
            error.message.includes('Cannot access before initialization') ||
            error.message.includes('is not a constructor')
          )
        )) {
          retryCount++;
          const delay = retryDelay * Math.pow(2, retryCount - 1); // Exponential backoff
          console.log(`🔄 HMR/module factory error detected, retrying (${retryCount}/${maxRetries}) in ${delay}ms...`);
          console.log(`   Error details: ${error.message}`);
          setTimeout(initWallets, delay);
          return;
        }

        setWallets([]);
      } finally {
        setIsLoading(false);
      }
    };

    // Only initialize on client-side
    if (typeof window !== 'undefined') {
      initWallets();
    } else {
      setIsLoading(false);
    }

    // HMR cleanup and re-initialization
    const handleHMR = () => {
      console.log('🔄 HMR detected, reinitializing wallets...');
      retryCount = 0; // Reset retry count on HMR
      initWallets();
    };

    // Listen for HMR events if available
    if (typeof window !== 'undefined' && (window as any).__webpack_require__) {
      (window as any).__webpack_require__.cache = (window as any).__webpack_require__.cache || {};
    }

    return () => {
      // Cleanup on unmount
      setWallets([]);
      setError(null);
    };
  }, []);

  // Memoize wallets to prevent unnecessary re-renders during HMR
  const memoizedWallets = useMemo(() => {
    // Filter out any invalid wallet adapters that might have been corrupted by HMR
    return wallets.filter(wallet => {
      try {
        // Basic validation that the wallet adapter is still functional
        return wallet && typeof wallet.name === 'string' && typeof wallet.connect === 'function';
      } catch (e) {
        console.warn('⚠️ Filtering out corrupted wallet adapter:', e);
        return false;
      }
    });
  }, [wallets]);

  // Show loading state during wallet initialization
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-500 mx-auto mb-4"></div>
          <div className="text-white">Loading wallet adapters...</div>
          {error && (
            <div className="text-red-400 text-sm mt-2">
              {error}
            </div>
          )}
        </div>
      </div>
    );
  }

  // Enhanced error handling for HMR-related issues
  const enhancedWalletConfig = {
    ...walletConfig,
    onError: (error: Error) => {
      console.error('Wallet error:', error);

      // Enhanced HMR-specific error handling
      if (error.message.includes('module factory') ||
          error.message.includes('HMR') ||
          error.message.includes('chunk') ||
          error.message.includes('Cannot access before initialization') ||
          error.message.includes('is not a constructor')) {
        console.log('🔄 HMR/module factory related wallet error detected, attempting recovery...');
        console.log(`   Error details: ${error.message}`);

        // Try to reinitialize wallets first before reloading
        try {
          console.log('🔄 Attempting wallet re-initialization...');
          // Trigger a re-initialization of the wallet system
          setTimeout(() => {
            window.location.reload();
          }, 2000); // Give some time for potential recovery
        } catch (recoveryError) {
          console.error('❌ Recovery attempt failed, forcing page reload');
          window.location.reload();
        }
        return;
      }

      // Handle other wallet errors
      if (error.name === 'WalletNotReadyError') {
        console.warn('Wallet not ready. Please ensure your wallet extension is installed and unlocked.');
      } else if (error.name === 'WalletConnectionError') {
        console.warn('Failed to connect to wallet. Please try again.');
      }

      // Call original error handler if provided
      if (walletConfig.onError) {
        walletConfig.onError(error);
      }
    },
  };

  return (
    <ConnectionProvider endpoint={endpoint}>
      <SolanaWalletProvider
        wallets={memoizedWallets}
        autoConnect={enhancedWalletConfig.autoConnect}
        onError={enhancedWalletConfig.onError}
        localStorageKey={enhancedWalletConfig.localStorageKey}
      >
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </SolanaWalletProvider>
    </ConnectionProvider>
  );
}
