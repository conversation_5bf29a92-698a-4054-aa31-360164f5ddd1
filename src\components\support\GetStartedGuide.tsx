'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import { ShimmerText, PulseDot, BreathingCard } from '@/components/ui/LiveIndicator';
import { DEGEN_EMOJIS } from '@/lib/degen-terminology';
import Icon from '@/components/ui/Icon';
import { cn } from '@/lib/utils';

interface GuideStep {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  gradient: string;
  steps: {
    title: string;
    description: string;
    tips?: string[];
  }[];
}

export function GetStartedGuide() {
  const [activeStep, setActiveStep] = useState<string>('wallet');
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const guideSteps: GuideStep[] = [
    {
      id: 'wallet',
      title: 'Connect Your Wallet',
      description: 'Set up your Solana wallet to start trading',
      icon: 'wallet',
      color: 'text-degen-blue',
      gradient: 'from-degen-blue to-degen-purple',
      steps: [
        {
          title: 'Install a Solana Wallet',
          description: 'Download and install Phantom, Solflare, or Backpack wallet extension',
          tips: [
            'Phantom is the most popular choice for beginners',
            'Make sure to download from official websites only',
            'Create a strong password and backup your seed phrase'
          ]
        },
        {
          title: 'Fund Your Wallet',
          description: 'Add SOL to your wallet for trading and transaction fees',
          tips: [
            'You need SOL for transaction fees (usually 0.001-0.01 SOL per trade)',
            'Start with a small amount to test the system',
            'Keep some SOL aside for fees when trading'
          ]
        },
        {
          title: 'Connect to Signal V1',
          description: 'Click the wallet button in the top right to connect',
          tips: [
            'Make sure your wallet is unlocked',
            'Approve the connection request',
            'Your wallet address will appear once connected'
          ]
        }
      ]
    },
    {
      id: 'signals',
      title: 'Set Up Signal Monitoring',
      description: 'Configure Telegram signal detection',
      icon: 'signals',
      color: 'text-degen-purple',
      gradient: 'from-degen-purple to-degen-gold',
      steps: [
        {
          title: 'Navigate to Live Alpha',
          description: 'Go to the Live Alpha page to see incoming signals',
          tips: [
            'Signals appear in real-time from monitored Telegram channels',
            'Each signal shows token info, market cap, and contract address',
            'Green indicators show active signal monitoring'
          ]
        },
        {
          title: 'Understand Signal Cards',
          description: 'Learn how to read and interpret signal information',
          tips: [
            'Token alerts show which coin was mentioned',
            'Contract addresses are truncated for security',
            'Market cap helps assess token size and risk'
          ]
        },
        {
          title: 'Copy Interesting Signals',
          description: 'Use the Copy button to save contract addresses for later',
          tips: [
            'Copied addresses are saved to your clipboard',
            'You can paste them into trading platforms',
            'Use this to track promising opportunities'
          ]
        }
      ]
    },
    {
      id: 'trading',
      title: 'Configure Trading',
      description: 'Set up automated trading parameters',
      icon: 'trading',
      color: 'text-degen-green',
      gradient: 'from-degen-green to-degen-blue',
      steps: [
        {
          title: 'Go to Diamond Hands',
          description: 'Access the trading configuration page',
          tips: [
            'This is where you set up automated trading rules',
            'Start with conservative settings',
            'You can always adjust parameters later'
          ]
        },
        {
          title: 'Set Trade Amounts',
          description: 'Configure how much SOL to trade per signal',
          tips: [
            'Start with small amounts (0.1-0.5 SOL)',
            'Never risk more than you can afford to lose',
            'Consider setting a maximum daily trading limit'
          ]
        },
        {
          title: 'Configure Risk Management',
          description: 'Set stop-loss and take-profit levels',
          tips: [
            'Stop-loss helps limit losses on bad trades',
            'Take-profit secures gains automatically',
            'Slippage tolerance affects trade execution'
          ]
        }
      ]
    },
    {
      id: 'portfolio',
      title: 'Monitor Your Bag',
      description: 'Track your positions and performance',
      icon: 'portfolio',
      color: 'text-degen-gold',
      gradient: 'from-degen-gold to-degen-green',
      steps: [
        {
          title: 'Check Your Portfolio',
          description: 'Visit My Bag to see all your positions',
          tips: [
            'Green numbers indicate profits (gains)',
            'Red numbers show losses',
            'Multiplier shows how much your investment has grown'
          ]
        },
        {
          title: 'Review Trade History',
          description: 'Analyze your past trades and performance',
          tips: [
            'Learn from both winning and losing trades',
            'Look for patterns in successful signals',
            'Adjust your strategy based on results'
          ]
        },
        {
          title: 'Manage Positions',
          description: 'Decide when to hold or sell your tokens',
          tips: [
            'Diamond hands means holding for bigger gains',
            'Paper hands means selling quickly for small profits',
            'Know your risk tolerance and stick to your plan'
          ]
        }
      ]
    }
  ];

  const markStepComplete = (stepId: string) => {
    setCompletedSteps(prev => new Set([...prev, stepId]));
  };

  const currentGuideStep = guideSteps.find(step => step.id === activeStep);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <ShimmerText speed="slow">
          <h2 className="text-3xl font-bold text-primary">
            Get Started Guide
          </h2>
        </ShimmerText>
        <p className="mt-2 text-lg text-degen-purple font-medium">
          Your path to becoming a Signal V1 degen {DEGEN_EMOJIS.rocket}
        </p>
        <div className="flex items-center justify-center gap-2 mt-2">
          <PulseDot color="blue" size="sm" />
          <PulseDot color="purple" size="sm" />
          <PulseDot color="green" size="sm" />
        </div>
      </div>

      {/* Progress Overview */}
      <Card className="bg-gradient-sophisticated border-gradient-blue">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-bold text-degen-blue">Progress Overview</h3>
            <Badge variant="moon" className="font-bold">
              {completedSteps.size}/{guideSteps.length} Complete
            </Badge>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            {guideSteps.map((step) => (
              <Button
                key={step.id}
                variant={activeStep === step.id ? "ape" : completedSteps.has(step.id) ? "moon" : "outline"}
                size="sm"
                onClick={() => setActiveStep(step.id)}
                className={cn(
                  "flex flex-col items-center p-3 h-auto",
                  activeStep === step.id && "glow-green"
                )}
              >
                <Icon 
                  name={step.icon} 
                  size="sm" 
                  color={activeStep === step.id ? "white" : completedSteps.has(step.id) ? "white" : "gray"} 
                  className="mb-1" 
                />
                <span className="text-xs font-medium text-center">{step.title}</span>
                {completedSteps.has(step.id) && (
                  <div className="text-xs text-degen-green mt-1">✓</div>
                )}
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Current Step Details */}
      {currentGuideStep && (
        <BreathingCard intensity="subtle">
          <Card className={`bg-gradient-sophisticated border-gradient-${currentGuideStep.color.split('-')[1]}`}>
            <CardHeader>
              <CardTitle className={`flex items-center gap-3 ${currentGuideStep.color}`}>
                <Icon name={currentGuideStep.icon} size="md" color={currentGuideStep.color.split('-')[1]} />
                <div>
                  <h3 className="text-xl font-bold">{currentGuideStep.title}</h3>
                  <p className="text-sm text-degen-gray font-normal mt-1">
                    {currentGuideStep.description}
                  </p>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {currentGuideStep.steps.map((step, index) => (
                <div key={index} className="space-y-3">
                  <div className="flex items-start gap-3">
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${currentGuideStep.gradient} flex items-center justify-center text-white font-bold text-sm`}>
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-bold text-primary mb-1">{step.title}</h4>
                      <p className="text-degen-gray text-sm">{step.description}</p>
                      {step.tips && (
                        <div className="mt-3 space-y-1">
                          <p className="text-xs font-medium text-degen-blue">💡 Pro Tips:</p>
                          <ul className="space-y-1">
                            {step.tips.map((tip, tipIndex) => (
                              <li key={tipIndex} className="text-xs text-degen-gray flex items-start gap-2">
                                <span className="text-degen-gold">•</span>
                                <span>{tip}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                  {index < currentGuideStep.steps.length - 1 && (
                    <div className="ml-4 w-px h-4 bg-gradient-to-b from-degen-purple to-transparent"></div>
                  )}
                </div>
              ))}
              
              <div className="flex items-center justify-between pt-4 border-t border-degen-purple/30">
                <div className="flex gap-2">
                  {activeStep !== guideSteps[0].id && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const currentIndex = guideSteps.findIndex(s => s.id === activeStep);
                        if (currentIndex > 0) {
                          setActiveStep(guideSteps[currentIndex - 1].id);
                        }
                      }}
                    >
                      <Icon name="chevronLeft" size="sm" color="gray" className="mr-1" />
                      Previous
                    </Button>
                  )}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="moon"
                    size="sm"
                    onClick={() => markStepComplete(activeStep)}
                    disabled={completedSteps.has(activeStep)}
                  >
                    {completedSteps.has(activeStep) ? (
                      <>
                        <Icon name="check" size="sm" color="white" className="mr-1" />
                        Completed
                      </>
                    ) : (
                      <>
                        <Icon name="check" size="sm" color="white" className="mr-1" />
                        Mark Complete
                      </>
                    )}
                  </Button>
                  
                  {activeStep !== guideSteps[guideSteps.length - 1].id && (
                    <Button
                      variant="ape"
                      size="sm"
                      onClick={() => {
                        const currentIndex = guideSteps.findIndex(s => s.id === activeStep);
                        if (currentIndex < guideSteps.length - 1) {
                          setActiveStep(guideSteps[currentIndex + 1].id);
                        }
                      }}
                    >
                      Next
                      <Icon name="chevronRight" size="sm" color="white" className="ml-1" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </BreathingCard>
      )}

      {/* Completion Message */}
      {completedSteps.size === guideSteps.length && (
        <BreathingCard intensity="medium">
          <Card className="bg-gradient-sophisticated border-gradient-green text-center">
            <CardContent className="p-6">
              <div className="text-4xl mb-3">{DEGEN_EMOJIS.diamond}</div>
              <h3 className="text-xl font-bold text-degen-green mb-2">
                Congratulations, Diamond Hands! {DEGEN_EMOJIS.rocket}
              </h3>
              <p className="text-degen-gray mb-4">
                You've completed the Signal V1 onboarding guide. You're now ready to start your journey to the moon!
              </p>
              <Button variant="ape" className="font-bold">
                <Icon name="rocket" size="sm" color="white" className="mr-1" />
                Start Trading
              </Button>
            </CardContent>
          </Card>
        </BreathingCard>
      )}
    </div>
  );
}
